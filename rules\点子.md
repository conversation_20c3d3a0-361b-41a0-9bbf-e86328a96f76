计划引用Grapes js 开发一个网页图书编辑器，可以排版和编写图书，充分体现所见即所得的方式，使用React + GrapesJS +electron前端 + Django 后端，只开发电脑桌面端，包括但不限于可以制定多种排版样式快捷编排的功能，实现点击和拖拽完成排版，图书为在线编写，全部数据存在服务器，自动保存，体现图书的创建流程和图书的完整结构，AI辅助编写，AI辅助排版d等排版和编写功能  
编写详细的包括但不限于项目故事、场景、整体方案等
D:\htmlEditor\rules\htmleditor整体方案方案.md

请基于 GrapesJS 开发一个功能完整的桌面端图书编辑器系统，技术栈为 React + GrapesJS + Electron 前端 + Django 后端。

**核心功能需求：**
1. **所见即所得编辑器**：基于 GrapesJS 实现可视化编辑界面，支持实时预览
2. **拖拽式排版**：支持点击和拖拽操作完成页面布局和内容排版
3. **多样式模板系统**：提供多种预设排版样式和模板，支持快捷应用和自定义
4. **在线协作编写**：支持多用户在线编辑，所有数据实时存储在服务器端
5. **自动保存机制**：实现增量保存和版本控制，防止数据丢失
6. **完整图书结构管理**：支持章节、页面、目录等图书结构化管理，体现完整的图书创建流程
7. **AI 辅助功能**：集成 AI 辅助内容生成和智能排版建议

**技术架构要求：**
- 前端：React + GrapesJS + Electron（桌面应用）
- 后端：Django + Django REST Framework
- 数据库：支持复杂数据结构存储（推荐 PostgreSQL）
- 实时通信：WebSocket 支持实时协作
- 部署：仅开发桌面端应用，无需考虑移动端适配

**交付物要求：**
请编写详细的项目文档，包含以下内容并保存到 `D:\htmlEditor\rules\htmleditor整体方案.md`：
1. **项目背景与故事**：阐述项目的市场需求、目标用户和应用场景
2. **用户场景分析**：详细描述目标用户群体和典型使用流程
3. **技术架构设计**：系统整体架构、模块划分、技术选型理由（特别说明 Electron 集成方案）
4. **功能模块详细设计**：每个核心功能的实现方案和技术细节
5. **数据库设计**：数据模型和关系设计，支持图书结构化存储
6. **API 接口设计**：前后端交互接口规范，包括实时协作接口
7. **Electron 桌面应用方案**：桌面端特有功能和集成策略
8. **部署和运维方案**：生产环境部署策略和桌面应用分发
9. **开发计划和里程碑**：项目实施的时间规划和阶段目标

请确保方案具有可执行性，技术选型合理，并充分考虑桌面应用的特殊需求、系统的可扩展性和维护性。