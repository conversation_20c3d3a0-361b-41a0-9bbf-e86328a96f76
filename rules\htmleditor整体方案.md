# 桌面端图书编辑器系统整体方案

## 1. 项目背景与故事

### 1.1 市场需求分析

在数字化出版时代，传统的图书编辑工具已无法满足现代作者和出版社的需求。市场上存在以下痛点：

- **工具分散**：作者需要在多个软件间切换（Word写作、InDesign排版、Photoshop处理图片）
- **协作困难**：传统工具缺乏实时协作功能，版本管理混乱
- **排版复杂**：非专业用户难以掌握专业排版软件
- **格式兼容**：不同平台间的格式转换问题频发
- **成本高昂**：专业排版软件价格昂贵，中小型出版社负担重

### 1.2 目标用户群体

**主要用户群体：**
- **独立作者**：自出版作者，需要一站式的写作和排版工具
- **小型出版社**：预算有限，需要高效的图书制作工具
- **教育机构**：制作教材、讲义等教学资料
- **企业用户**：制作培训手册、产品文档等

**次要用户群体：**
- **设计师**：需要快速制作图书原型
- **内容创作者**：制作电子书、在线教程等

### 1.3 应用场景

- **小说创作**：从构思、写作到排版出版的完整流程
- **技术文档**：API文档、用户手册等技术资料制作
- **学术出版**：论文集、学术专著的编辑和排版
- **教材制作**：教科书、练习册等教学材料
- **企业出版**：年报、产品手册、培训资料等

## 2. 用户场景分析

### 2.1 典型用户画像

**用户A：独立小说作者（张小说）**
- 年龄：28岁，文学专业毕业
- 需求：希望有一个工具能够从写作到出版一站式完成
- 痛点：不懂复杂的排版软件，希望所见即所得
- 使用场景：在家写作，偶尔与编辑协作修改

**用户B：小型出版社编辑（李编辑）**
- 年龄：35岁，出版行业5年经验
- 需求：需要高效的多人协作编辑工具
- 痛点：版本管理混乱，与作者沟通成本高
- 使用场景：办公室工作，需要与多个作者和设计师协作

**用户C：技术文档作者（王工程师）**
- 年龄：32岁，软件工程师
- 需求：制作API文档和用户手册
- 痛点：需要频繁更新，希望能自动化部分流程
- 使用场景：远程工作，需要与团队实时协作

### 2.2 典型使用流程

**流程1：新书创作流程**
1. 创建新图书项目，选择模板（小说、技术文档等）
2. 设置图书基本信息（标题、作者、封面等）
3. 创建章节结构，规划内容框架
4. 使用可视化编辑器进行内容创作
5. 实时预览排版效果，调整样式
6. 邀请协作者进行审阅和修改
7. 版本管理和变更追踪
8. 最终导出为多种格式（PDF、EPUB等）

**流程2：协作编辑流程**
1. 接收协作邀请，加入图书项目
2. 查看当前编辑状态和其他用户位置
3. 选择特定章节或页面进行编辑
4. 实时看到其他用户的修改
5. 添加评论和建议
6. 处理编辑冲突
7. 提交修改并通知相关人员

## 3. 技术架构设计

### 3.1 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    桌面端应用 (Electron)                      │
├─────────────────────────────────────────────────────────────┤
│  主进程 (Main Process)                                       │
│  ├── 应用生命周期管理                                         │
│  ├── 窗口管理和原生菜单                                       │
│  ├── 文件系统操作                                           │
│  ├── 自动更新机制                                           │
│  └── IPC 通信管理                                           │
├─────────────────────────────────────────────────────────────┤
│  渲染进程 (Renderer Process)                                 │
│  ├── React 应用框架                                         │
│  │   ├── 路由管理 (React Router)                           │
│  │   ├── 状态管理 (Zustand)                               │
│  │   ├── UI 组件库 (Ant Design)                           │
│  │   └── 业务组件                                         │
│  ├── GrapesJS 编辑器层                                     │
│  │   ├── 核心编辑器 (@grapesjs/react)                     │
│  │   ├── 自定义插件和组件                                   │
│  │   ├── 样式管理器                                       │
│  │   └── 资源管理器                                       │
│  └── 数据管理层                                           │
│      ├── API 客户端 (Axios)                               │
│      ├── WebSocket 客户端                                 │
│      ├── 本地缓存 (IndexedDB)                             │
│      └── 离线数据同步                                     │
└─────────────────────────────────────────────────────────────┘
                              │
                              │ HTTP/WebSocket
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      后端服务 (Django)                       │
├─────────────────────────────────────────────────────────────┤
│  Web 服务层                                                 │
│  ├── API 网关 (Nginx)                                      │
│  ├── 负载均衡                                             │
│  └── SSL 终端                                             │
├─────────────────────────────────────────────────────────────┤
│  应用服务层                                                 │
│  ├── Django REST Framework                                 │
│  │   ├── 认证和授权                                       │
│  │   ├── 图书管理 API                                     │
│  │   ├── 协作编辑 API                                     │
│  │   └── 模板管理 API                                     │
│  ├── Django Channels (WebSocket)                           │
│  │   ├── 实时协作处理                                     │
│  │   ├── 用户状态管理                                     │
│  │   └── 消息广播                                         │
│  └── 业务逻辑层                                           │
│      ├── 图书结构管理                                     │
│      ├── 版本控制系统                                     │
│      ├── 权限管理                                         │
│      └── AI 服务集成                                      │
├─────────────────────────────────────────────────────────────┤
│  数据存储层                                                 │
│  ├── PostgreSQL (主数据库)                                 │
│  ├── Redis (缓存和会话)                                    │
│  ├── MinIO/S3 (文件存储)                                   │
│  └── Elasticsearch (全文搜索)                              │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 技术选型理由

**前端技术栈：**
- **Electron**：提供原生桌面体验，支持跨平台部署
- **React**：成熟的前端框架，生态丰富，与GrapesJS集成良好
- **TypeScript**：提供类型安全，提高代码质量和维护性
- **@grapesjs/react**：官方React包装器，确保最佳集成体验
- **Zustand**：轻量级状态管理，比Redux更简单
- **Ant Design**：企业级UI组件库，适合桌面应用

**后端技术栈：**
- **Django**：成熟的Python Web框架，开发效率高
- **Django REST Framework**：强大的API框架
- **Django Channels**：支持WebSocket实时通信
- **PostgreSQL**：支持JSON字段，适合复杂数据结构
- **Redis**：高性能缓存和会话存储
- **Celery**：异步任务处理（AI服务、文件转换等）

### 3.3 模块划分

**前端模块：**
1. **应用核心模块**：应用初始化、路由管理、全局状态
2. **编辑器模块**：GrapesJS集成、自定义组件、插件系统
3. **项目管理模块**：图书创建、章节管理、页面导航
4. **协作模块**：实时编辑、用户状态、冲突处理
5. **模板模块**：模板库、自定义模板、样式主题
6. **工具模块**：导入导出、打印、AI辅助
7. **设置模块**：用户偏好、应用配置、快捷键

**后端模块：**
1. **认证模块**：用户管理、权限控制、会话管理
2. **图书管理模块**：项目CRUD、结构管理、元数据
3. **内容管理模块**：页面内容、版本控制、资源管理
4. **协作模块**：实时通信、冲突解决、状态同步
5. **模板模块**：模板库、分类管理、分享机制
6. **AI服务模块**：内容生成、智能建议、语言处理
7. **导出模块**：格式转换、文件生成、下载管理

## 4. 功能模块详细设计

### 4.1 核心编辑器模块

**基于GrapesJS的可视化编辑器：**
```typescript
// 编辑器初始化配置
const editorConfig = {
  container: '#gjs',
  height: '100vh',
  width: 'auto',
  storageManager: false, // 使用自定义存储
  blockManager: {
    appendTo: '#blocks',
    blocks: [
      // 图书专用组件块
      {
        id: 'book-title',
        label: '图书标题',
        content: '<h1 class="book-title">图书标题</h1>',
        category: '图书元素'
      },
      {
        id: 'chapter-title', 
        label: '章节标题',
        content: '<h2 class="chapter-title">章节标题</h2>',
        category: '图书元素'
      },
      {
        id: 'paragraph',
        label: '段落',
        content: '<p class="paragraph">段落内容</p>',
        category: '文本元素'
      },
      {
        id: 'quote',
        label: '引用',
        content: '<blockquote class="quote">引用内容</blockquote>',
        category: '文本元素'
      },
      {
        id: 'footnote',
        label: '脚注',
        content: '<span class="footnote">脚注内容</span>',
        category: '文本元素'
      }
    ]
  },
  styleManager: {
    appendTo: '#styles',
    sectors: [
      {
        name: '排版',
        open: false,
        buildProps: ['font-family', 'font-size', 'line-height', 'text-align', 'text-indent']
      },
      {
        name: '布局',
        open: false, 
        buildProps: ['margin', 'padding', 'width', 'height']
      },
      {
        name: '装饰',
        open: false,
        buildProps: ['color', 'background-color', 'border', 'border-radius']
      }
    ]
  },
  layerManager: {
    appendTo: '#layers'
  },
  traitManager: {
    appendTo: '#traits'
  }
};
```

**自定义图书专用组件：**
```typescript
// 页码组件
editor.DomComponents.addType('page-number', {
  model: {
    defaults: {
      tagName: 'span',
      classes: ['page-number'],
      content: '{{pageNumber}}',
      traits: [
        {
          type: 'select',
          label: '页码格式',
          name: 'format',
          options: [
            { value: 'number', name: '数字' },
            { value: 'roman', name: '罗马数字' },
            { value: 'alpha', name: '字母' }
          ]
        }
      ]
    }
  },
  view: {
    onRender() {
      // 页码渲染逻辑
    }
  }
});

// 目录组件
editor.DomComponents.addType('table-of-contents', {
  model: {
    defaults: {
      tagName: 'div',
      classes: ['toc'],
      content: '<div class="toc-title">目录</div><ul class="toc-list"></ul>',
      traits: [
        {
          type: 'checkbox',
          label: '自动生成',
          name: 'auto-generate'
        },
        {
          type: 'number',
          label: '最大层级',
          name: 'max-level',
          min: 1,
          max: 6
        }
      ]
    }
  }
});
```

### 4.2 桌面端特有功能模块

**原生菜单栏和快捷键：**
```javascript
// main.js - Electron 主进程菜单配置
const { Menu, dialog, ipcMain } = require('electron');

const menuTemplate = [
  {
    label: '文件',
    submenu: [
      {
        label: '新建图书',
        accelerator: 'CmdOrCtrl+N',
        click: () => {
          mainWindow.webContents.send('menu:new-book');
        }
      },
      {
        label: '打开图书',
        accelerator: 'CmdOrCtrl+O',
        click: async () => {
          const result = await dialog.showOpenDialog(mainWindow, {
            properties: ['openFile'],
            filters: [
              { name: '图书文件', extensions: ['book', 'json'] },
              { name: '所有文件', extensions: ['*'] }
            ]
          });
          if (!result.canceled) {
            mainWindow.webContents.send('menu:open-book', result.filePaths[0]);
          }
        }
      },
      {
        label: '保存',
        accelerator: 'CmdOrCtrl+S',
        click: () => {
          mainWindow.webContents.send('menu:save-book');
        }
      },
      {
        label: '另存为',
        accelerator: 'CmdOrCtrl+Shift+S',
        click: () => {
          mainWindow.webContents.send('menu:save-book-as');
        }
      },
      { type: 'separator' },
      {
        label: '导入',
        submenu: [
          {
            label: '从Word导入',
            click: () => {
              mainWindow.webContents.send('menu:import-word');
            }
          },
          {
            label: '从Markdown导入',
            click: () => {
              mainWindow.webContents.send('menu:import-markdown');
            }
          }
        ]
      },
      {
        label: '导出',
        submenu: [
          {
            label: '导出为PDF',
            click: () => {
              mainWindow.webContents.send('menu:export-pdf');
            }
          },
          {
            label: '导出为EPUB',
            click: () => {
              mainWindow.webContents.send('menu:export-epub');
            }
          },
          {
            label: '导出为HTML',
            click: () => {
              mainWindow.webContents.send('menu:export-html');
            }
          }
        ]
      },
      { type: 'separator' },
      {
        label: '打印',
        accelerator: 'CmdOrCtrl+P',
        click: () => {
          mainWindow.webContents.print();
        }
      }
    ]
  },
  {
    label: '编辑',
    submenu: [
      {
        label: '撤销',
        accelerator: 'CmdOrCtrl+Z',
        click: () => {
          mainWindow.webContents.send('menu:undo');
        }
      },
      {
        label: '重做',
        accelerator: 'CmdOrCtrl+Y',
        click: () => {
          mainWindow.webContents.send('menu:redo');
        }
      },
      { type: 'separator' },
      {
        label: '复制',
        accelerator: 'CmdOrCtrl+C',
        role: 'copy'
      },
      {
        label: '粘贴',
        accelerator: 'CmdOrCtrl+V',
        role: 'paste'
      },
      {
        label: '全选',
        accelerator: 'CmdOrCtrl+A',
        role: 'selectall'
      }
    ]
  },
  {
    label: '视图',
    submenu: [
      {
        label: '缩放',
        submenu: [
          {
            label: '放大',
            accelerator: 'CmdOrCtrl+Plus',
            click: () => {
              mainWindow.webContents.send('menu:zoom-in');
            }
          },
          {
            label: '缩小',
            accelerator: 'CmdOrCtrl+-',
            click: () => {
              mainWindow.webContents.send('menu:zoom-out');
            }
          },
          {
            label: '重置缩放',
            accelerator: 'CmdOrCtrl+0',
            click: () => {
              mainWindow.webContents.send('menu:zoom-reset');
            }
          }
        ]
      },
      { type: 'separator' },
      {
        label: '全屏',
        accelerator: 'F11',
        click: () => {
          mainWindow.setFullScreen(!mainWindow.isFullScreen());
        }
      },
      {
        label: '开发者工具',
        accelerator: 'F12',
        click: () => {
          mainWindow.webContents.toggleDevTools();
        }
      }
    ]
  },
  {
    label: '工具',
    submenu: [
      {
        label: 'AI助手',
        accelerator: 'CmdOrCtrl+Shift+A',
        click: () => {
          mainWindow.webContents.send('menu:ai-assistant');
        }
      },
      {
        label: '拼写检查',
        click: () => {
          mainWindow.webContents.send('menu:spell-check');
        }
      },
      {
        label: '字数统计',
        click: () => {
          mainWindow.webContents.send('menu:word-count');
        }
      },
      { type: 'separator' },
      {
        label: '偏好设置',
        accelerator: 'CmdOrCtrl+,',
        click: () => {
          mainWindow.webContents.send('menu:preferences');
        }
      }
    ]
  }
];

const menu = Menu.buildFromTemplate(menuTemplate);
Menu.setApplicationMenu(menu);
```

**本地文件系统集成：**
```javascript
// 文件操作 IPC 处理
ipcMain.handle('file:save-book', async (event, bookData) => {
  try {
    const result = await dialog.showSaveDialog(mainWindow, {
      defaultPath: `${bookData.title}.book`,
      filters: [
        { name: '图书文件', extensions: ['book'] },
        { name: 'JSON文件', extensions: ['json'] }
      ]
    });

    if (!result.canceled) {
      const fs = require('fs').promises;
      await fs.writeFile(result.filePath, JSON.stringify(bookData, null, 2));
      return { success: true, path: result.filePath };
    }
    return { success: false, cancelled: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('file:open-book', async (event, filePath) => {
  try {
    const fs = require('fs').promises;
    const data = await fs.readFile(filePath, 'utf8');
    const bookData = JSON.parse(data);
    return { success: true, data: bookData };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// 拖拽文件处理
ipcMain.handle('file:handle-drop', async (event, files) => {
  const processedFiles = [];

  for (const file of files) {
    const path = require('path');
    const ext = path.extname(file.path).toLowerCase();

    if (['.jpg', '.jpeg', '.png', '.gif', '.svg'].includes(ext)) {
      // 处理图片文件
      const fs = require('fs').promises;
      const buffer = await fs.readFile(file.path);
      const base64 = buffer.toString('base64');

      processedFiles.push({
        type: 'image',
        name: path.basename(file.path),
        data: `data:image/${ext.slice(1)};base64,${base64}`,
        size: file.size
      });
    } else if (['.docx', '.doc'].includes(ext)) {
      // 处理Word文档
      processedFiles.push({
        type: 'document',
        name: path.basename(file.path),
        path: file.path
      });
    }
  }

  return processedFiles;
});
```

### 4.3 图书结构管理模块

**图书项目数据结构：**
```typescript
interface BookProject {
  id: string;
  title: string;
  description: string;
  author: string;
  coverImage?: string;
  settings: BookSettings;
  chapters: Chapter[];
  metadata: BookMetadata;
  createdAt: Date;
  updatedAt: Date;
}

interface BookSettings {
  pageSize: 'A4' | 'A5' | 'Letter' | 'Custom';
  margins: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
  fonts: {
    primary: string;
    secondary: string;
    monospace: string;
  };
  theme: string;
  language: string;
}

interface Chapter {
  id: string;
  title: string;
  order: number;
  pages: Page[];
  isVisible: boolean;
  parent?: string; // 支持嵌套章节
  children?: string[];
}

interface Page {
  id: string;
  title: string;
  content: any; // GrapesJS 组件数据
  styles: any;  // CSS 样式数据
  assets: Asset[];
  order: number;
  isPublished: boolean;
  wordCount: number;
  lastModified: Date;
  lastModifiedBy: string;
}
```

**章节管理组件：**
```typescript
import React, { useState } from 'react';
import { Tree, Button, Modal, Form, Input, message } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';

interface ChapterManagerProps {
  chapters: Chapter[];
  onChapterAdd: (chapter: Partial<Chapter>) => void;
  onChapterUpdate: (id: string, updates: Partial<Chapter>) => void;
  onChapterDelete: (id: string) => void;
  onChapterReorder: (chapters: Chapter[]) => void;
}

const ChapterManager: React.FC<ChapterManagerProps> = ({
  chapters,
  onChapterAdd,
  onChapterUpdate,
  onChapterDelete,
  onChapterReorder
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingChapter, setEditingChapter] = useState<Chapter | null>(null);
  const [form] = Form.useForm();

  const handleAddChapter = () => {
    setEditingChapter(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEditChapter = (chapter: Chapter) => {
    setEditingChapter(chapter);
    form.setFieldsValue(chapter);
    setIsModalVisible(true);
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (editingChapter) {
        onChapterUpdate(editingChapter.id, values);
        message.success('章节更新成功');
      } else {
        onChapterAdd({
          ...values,
          id: generateId(),
          order: chapters.length,
          pages: [],
          isVisible: true
        });
        message.success('章节添加成功');
      }

      setIsModalVisible(false);
    } catch (error) {
      message.error('操作失败');
    }
  };

  const treeData = chapters.map(chapter => ({
    title: (
      <div className="chapter-item">
        <span>{chapter.title}</span>
        <div className="chapter-actions">
          <Button
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEditChapter(chapter)}
          />
          <Button
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => onChapterDelete(chapter.id)}
          />
        </div>
      </div>
    ),
    key: chapter.id,
    children: chapter.pages?.map(page => ({
      title: page.title,
      key: page.id,
      isLeaf: true
    }))
  }));

  return (
    <div className="chapter-manager">
      <div className="chapter-header">
        <h3>章节结构</h3>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAddChapter}
        >
          添加章节
        </Button>
      </div>

      <Tree
        treeData={treeData}
        draggable
        onDrop={(info) => {
          // 处理拖拽重排序
          const newChapters = reorderChapters(chapters, info);
          onChapterReorder(newChapters);
        }}
      />

      <Modal
        title={editingChapter ? '编辑章节' : '添加章节'}
        visible={isModalVisible}
        onOk={handleSubmit}
        onCancel={() => setIsModalVisible(false)}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="title"
            label="章节标题"
            rules={[{ required: true, message: '请输入章节标题' }]}
          >
            <Input placeholder="请输入章节标题" />
          </Form.Item>

          <Form.Item
            name="description"
            label="章节描述"
          >
            <Input.TextArea
              placeholder="请输入章节描述（可选）"
              rows={3}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};
```

### 4.4 协作编辑模块

**WebSocket 实时通信：**
```typescript
// WebSocket 客户端
class CollaborationClient {
  private ws: WebSocket | null = null;
  private bookId: string;
  private userId: string;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  constructor(bookId: string, userId: string) {
    this.bookId = bookId;
    this.userId = userId;
    this.connect();
  }

  private connect() {
    const wsUrl = `ws://localhost:8000/ws/book/${this.bookId}/`;
    this.ws = new WebSocket(wsUrl);

    this.ws.onopen = () => {
      console.log('WebSocket连接已建立');
      this.reconnectAttempts = 0;

      // 发送用户加入消息
      this.send({
        type: 'user_join',
        userId: this.userId,
        timestamp: Date.now()
      });
    };

    this.ws.onmessage = (event) => {
      const message = JSON.parse(event.data);
      this.handleMessage(message);
    };

    this.ws.onclose = () => {
      console.log('WebSocket连接已关闭');
      this.attemptReconnect();
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket错误:', error);
    };
  }

  private attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => {
        console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.connect();
      }, 1000 * this.reconnectAttempts);
    }
  }

  private handleMessage(message: any) {
    switch (message.type) {
      case 'user_join':
        this.onUserJoin(message);
        break;
      case 'user_leave':
        this.onUserLeave(message);
        break;
      case 'cursor_move':
        this.onCursorMove(message);
        break;
      case 'content_change':
        this.onContentChange(message);
        break;
      case 'selection_change':
        this.onSelectionChange(message);
        break;
      case 'lock_acquire':
        this.onLockAcquire(message);
        break;
      case 'lock_release':
        this.onLockRelease(message);
        break;
    }
  }

  // 发送内容变更
  sendContentChange(pageId: string, changes: any) {
    this.send({
      type: 'content_change',
      pageId,
      changes,
      userId: this.userId,
      timestamp: Date.now()
    });
  }

  // 发送光标位置
  sendCursorMove(pageId: string, position: any) {
    this.send({
      type: 'cursor_move',
      pageId,
      position,
      userId: this.userId,
      timestamp: Date.now()
    });
  }

  // 获取编辑锁
  acquireLock(pageId: string) {
    this.send({
      type: 'lock_acquire',
      pageId,
      userId: this.userId,
      timestamp: Date.now()
    });
  }

  // 释放编辑锁
  releaseLock(pageId: string) {
    this.send({
      type: 'lock_release',
      pageId,
      userId: this.userId,
      timestamp: Date.now()
    });
  }

  private send(message: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    }
  }

  // 事件处理器（由外部组件实现）
  onUserJoin: (message: any) => void = () => {};
  onUserLeave: (message: any) => void = () => {};
  onCursorMove: (message: any) => void = () => {};
  onContentChange: (message: any) => void = () => {};
  onSelectionChange: (message: any) => void = () => {};
  onLockAcquire: (message: any) => void = () => {};
  onLockRelease: (message: any) => void = () => {};

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
}
```

**冲突解决机制：**
```typescript
// 操作转换算法实现
class OperationalTransform {
  // 转换两个并发操作
  static transform(op1: Operation, op2: Operation): [Operation, Operation] {
    if (op1.type === 'insert' && op2.type === 'insert') {
      return this.transformInsertInsert(op1, op2);
    } else if (op1.type === 'insert' && op2.type === 'delete') {
      return this.transformInsertDelete(op1, op2);
    } else if (op1.type === 'delete' && op2.type === 'insert') {
      const [op2Prime, op1Prime] = this.transformInsertDelete(op2, op1);
      return [op1Prime, op2Prime];
    } else if (op1.type === 'delete' && op2.type === 'delete') {
      return this.transformDeleteDelete(op1, op2);
    }

    return [op1, op2];
  }

  private static transformInsertInsert(op1: InsertOperation, op2: InsertOperation): [InsertOperation, InsertOperation] {
    if (op1.position <= op2.position) {
      return [
        op1,
        { ...op2, position: op2.position + op1.content.length }
      ];
    } else {
      return [
        { ...op1, position: op1.position + op2.content.length },
        op2
      ];
    }
  }

  private static transformInsertDelete(op1: InsertOperation, op2: DeleteOperation): [InsertOperation, DeleteOperation] {
    if (op1.position <= op2.position) {
      return [
        op1,
        { ...op2, position: op2.position + op1.content.length }
      ];
    } else if (op1.position >= op2.position + op2.length) {
      return [
        { ...op1, position: op1.position - op2.length },
        op2
      ];
    } else {
      // 插入位置在删除范围内
      return [
        { ...op1, position: op2.position },
        op2
      ];
    }
  }

  private static transformDeleteDelete(op1: DeleteOperation, op2: DeleteOperation): [DeleteOperation, DeleteOperation] {
    if (op1.position + op1.length <= op2.position) {
      return [
        op1,
        { ...op2, position: op2.position - op1.length }
      ];
    } else if (op2.position + op2.length <= op1.position) {
      return [
        { ...op1, position: op1.position - op2.length },
        op2
      ];
    } else {
      // 删除范围重叠，需要复杂处理
      const start1 = op1.position;
      const end1 = op1.position + op1.length;
      const start2 = op2.position;
      const end2 = op2.position + op2.length;

      if (start1 <= start2 && end1 >= end2) {
        // op1 包含 op2
        return [
          { ...op1, length: op1.length - op2.length },
          { ...op2, length: 0 } // op2 被完全包含，变为空操作
        ];
      } else if (start2 <= start1 && end2 >= end1) {
        // op2 包含 op1
        return [
          { ...op1, length: 0 }, // op1 被完全包含，变为空操作
          { ...op2, length: op2.length - op1.length }
        ];
      } else {
        // 部分重叠，需要分割处理
        const overlapStart = Math.max(start1, start2);
        const overlapEnd = Math.min(end1, end2);
        const overlapLength = overlapEnd - overlapStart;

        return [
          { ...op1, length: op1.length - overlapLength },
          { ...op2, length: op2.length - overlapLength, position: Math.min(start1, start2) }
        ];
      }
    }
  }
}

interface Operation {
  type: 'insert' | 'delete' | 'retain';
  position: number;
  userId: string;
  timestamp: number;
}

interface InsertOperation extends Operation {
  type: 'insert';
  content: string;
}

interface DeleteOperation extends Operation {
  type: 'delete';
  length: number;
}
```

### 4.5 版本控制模块

**版本管理系统：**
```typescript
interface Version {
  id: string;
  pageId: string;
  versionNumber: number;
  content: any;
  styles: any;
  assets: Asset[];
  createdBy: string;
  createdAt: Date;
  comment: string;
  isMajor: boolean;
  parentVersion?: string;
}

class VersionManager {
  private versions: Map<string, Version[]> = new Map();
  private currentVersions: Map<string, string> = new Map();

  // 创建新版本
  async createVersion(pageId: string, content: any, styles: any, comment: string = '', isMajor: boolean = false): Promise<Version> {
    const pageVersions = this.versions.get(pageId) || [];
    const versionNumber = pageVersions.length + 1;

    const version: Version = {
      id: generateId(),
      pageId,
      versionNumber,
      content: JSON.parse(JSON.stringify(content)), // 深拷贝
      styles: JSON.parse(JSON.stringify(styles)),
      assets: [],
      createdBy: getCurrentUserId(),
      createdAt: new Date(),
      comment,
      isMajor,
      parentVersion: pageVersions.length > 0 ? pageVersions[pageVersions.length - 1].id : undefined
    };

    pageVersions.push(version);
    this.versions.set(pageId, pageVersions);
    this.currentVersions.set(pageId, version.id);

    // 保存到服务器
    await this.saveVersionToServer(version);

    return version;
  }

  // 获取页面的所有版本
  getVersions(pageId: string): Version[] {
    return this.versions.get(pageId) || [];
  }

  // 获取特定版本
  getVersion(pageId: string, versionId: string): Version | null {
    const versions = this.versions.get(pageId) || [];
    return versions.find(v => v.id === versionId) || null;
  }

  // 回滚到指定版本
  async rollbackToVersion(pageId: string, versionId: string): Promise<boolean> {
    const version = this.getVersion(pageId, versionId);
    if (!version) return false;

    // 创建回滚版本
    await this.createVersion(
      pageId,
      version.content,
      version.styles,
      `回滚到版本 ${version.versionNumber}`,
      true
    );

    return true;
  }

  // 比较两个版本
  compareVersions(version1: Version, version2: Version): VersionDiff {
    return {
      contentDiff: this.diffContent(version1.content, version2.content),
      stylesDiff: this.diffStyles(version1.styles, version2.styles),
      summary: this.generateDiffSummary(version1, version2)
    };
  }

  private diffContent(content1: any, content2: any): ContentDiff[] {
    // 实现内容差异算法
    const diffs: ContentDiff[] = [];

    // 简化的差异检测
    const components1 = this.flattenComponents(content1);
    const components2 = this.flattenComponents(content2);

    // 检测添加、删除、修改的组件
    for (const comp1 of components1) {
      const comp2 = components2.find(c => c.id === comp1.id);
      if (!comp2) {
        diffs.push({
          type: 'delete',
          componentId: comp1.id,
          oldValue: comp1,
          newValue: null
        });
      } else if (JSON.stringify(comp1) !== JSON.stringify(comp2)) {
        diffs.push({
          type: 'modify',
          componentId: comp1.id,
          oldValue: comp1,
          newValue: comp2
        });
      }
    }

    for (const comp2 of components2) {
      const comp1 = components1.find(c => c.id === comp2.id);
      if (!comp1) {
        diffs.push({
          type: 'add',
          componentId: comp2.id,
          oldValue: null,
          newValue: comp2
        });
      }
    }

    return diffs;
  }

  private async saveVersionToServer(version: Version): Promise<void> {
    try {
      await fetch(`/api/pages/${version.pageId}/versions/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getAuthToken()}`
        },
        body: JSON.stringify(version)
      });
    } catch (error) {
      console.error('保存版本失败:', error);
      throw error;
    }
  }
}

interface VersionDiff {
  contentDiff: ContentDiff[];
  stylesDiff: StyleDiff[];
  summary: string;
}

interface ContentDiff {
  type: 'add' | 'delete' | 'modify';
  componentId: string;
  oldValue: any;
  newValue: any;
}
```

**自动保存机制：**
```typescript
class AutoSaveManager {
  private saveInterval: number = 30000; // 30秒
  private pendingChanges: Map<string, any> = new Map();
  private saveTimer: NodeJS.Timeout | null = null;
  private isOnline: boolean = navigator.onLine;

  constructor() {
    this.setupOnlineStatusListener();
    this.startAutoSave();
  }

  // 记录页面变更
  recordChange(pageId: string, changes: any) {
    this.pendingChanges.set(pageId, {
      ...this.pendingChanges.get(pageId),
      ...changes,
      lastModified: Date.now()
    });

    // 如果有重要变更，立即保存
    if (this.isImportantChange(changes)) {
      this.saveImmediately(pageId);
    }
  }

  // 开始自动保存
  private startAutoSave() {
    this.saveTimer = setInterval(() => {
      this.performAutoSave();
    }, this.saveInterval);
  }

  // 执行自动保存
  private async performAutoSave() {
    if (this.pendingChanges.size === 0) return;

    const changesToSave = new Map(this.pendingChanges);
    this.pendingChanges.clear();

    for (const [pageId, changes] of changesToSave) {
      try {
        if (this.isOnline) {
          // 在线保存到服务器
          await this.saveToServer(pageId, changes);
        } else {
          // 离线保存到本地
          await this.saveToLocal(pageId, changes);
        }

        // 显示保存状态
        this.showSaveStatus(pageId, 'saved');
      } catch (error) {
        console.error(`保存页面 ${pageId} 失败:`, error);

        // 保存失败，重新加入待保存队列
        this.pendingChanges.set(pageId, changes);
        this.showSaveStatus(pageId, 'error');
      }
    }
  }

  // 立即保存
  private async saveImmediately(pageId: string) {
    const changes = this.pendingChanges.get(pageId);
    if (!changes) return;

    this.pendingChanges.delete(pageId);

    try {
      if (this.isOnline) {
        await this.saveToServer(pageId, changes);
      } else {
        await this.saveToLocal(pageId, changes);
      }

      this.showSaveStatus(pageId, 'saved');
    } catch (error) {
      console.error(`立即保存页面 ${pageId} 失败:`, error);
      this.pendingChanges.set(pageId, changes);
      this.showSaveStatus(pageId, 'error');
    }
  }

  // 保存到服务器
  private async saveToServer(pageId: string, changes: any): Promise<void> {
    const response = await fetch(`/api/pages/${pageId}/`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getAuthToken()}`
      },
      body: JSON.stringify(changes)
    });

    if (!response.ok) {
      throw new Error(`服务器保存失败: ${response.statusText}`);
    }
  }

  // 保存到本地
  private async saveToLocal(pageId: string, changes: any): Promise<void> {
    const { ipcRenderer } = window.require('electron');

    const result = await ipcRenderer.invoke('storage:save-page', {
      pageId,
      changes,
      timestamp: Date.now()
    });

    if (!result.success) {
      throw new Error(`本地保存失败: ${result.error}`);
    }
  }

  // 判断是否为重要变更
  private isImportantChange(changes: any): boolean {
    // 如果是结构性变更（添加/删除组件），立即保存
    if (changes.components && (
      changes.components.added?.length > 0 ||
      changes.components.removed?.length > 0
    )) {
      return true;
    }

    // 如果是大量文本变更，立即保存
    if (changes.content && changes.content.length > 1000) {
      return true;
    }

    return false;
  }

  // 显示保存状态
  private showSaveStatus(pageId: string, status: 'saving' | 'saved' | 'error') {
    const statusElement = document.querySelector(`[data-page-id="${pageId}"] .save-status`);
    if (statusElement) {
      statusElement.className = `save-status ${status}`;
      statusElement.textContent = {
        saving: '保存中...',
        saved: '已保存',
        error: '保存失败'
      }[status];
    }
  }

  // 监听网络状态
  private setupOnlineStatusListener() {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.syncOfflineChanges();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
    });
  }

  // 同步离线变更
  private async syncOfflineChanges() {
    const { ipcRenderer } = window.require('electron');

    try {
      const offlineChanges = await ipcRenderer.invoke('storage:get-offline-changes');

      for (const change of offlineChanges) {
        await this.saveToServer(change.pageId, change.changes);
        await ipcRenderer.invoke('storage:remove-offline-change', change.id);
      }

      console.log('离线变更同步完成');
    } catch (error) {
      console.error('离线变更同步失败:', error);
    }
  }

  // 停止自动保存
  stop() {
    if (this.saveTimer) {
      clearInterval(this.saveTimer);
      this.saveTimer = null;
    }
  }
}
```

## 5. 数据库设计

### 5.1 核心数据模型

**Django 模型定义：**
```python
from django.db import models
from django.contrib.auth.models import AbstractUser
from django.contrib.postgres.fields import JSONField
import uuid

class User(AbstractUser):
    """用户模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    avatar = models.ImageField(upload_to='avatars/', blank=True, null=True)
    subscription_type = models.CharField(
        max_length=20,
        choices=[
            ('free', '免费版'),
            ('pro', '专业版'),
            ('enterprise', '企业版')
        ],
        default='free'
    )
    storage_used = models.BigIntegerField(default=0)  # 已使用存储空间（字节）
    storage_limit = models.BigIntegerField(default=1024*1024*1024)  # 存储限制（1GB）
    created_at = models.DateTimeField(auto_now_add=True)
    last_active = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'users'

class Book(models.Model):
    """图书项目模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    cover_image = models.ImageField(upload_to='book_covers/', blank=True, null=True)

    # 关联关系
    owner = models.ForeignKey(User, on_delete=models.CASCADE, related_name='owned_books')
    collaborators = models.ManyToManyField(User, through='BookCollaborator', related_name='collaborated_books')

    # 图书设置
    settings = JSONField(default=dict)  # 页面设置、样式配置、导出选项等

    # 状态管理
    status = models.CharField(
        max_length=20,
        choices=[
            ('draft', '草稿'),
            ('review', '审阅中'),
            ('published', '已发布'),
            ('archived', '已归档')
        ],
        default='draft'
    )

    # 统计信息
    total_pages = models.IntegerField(default=0)
    total_words = models.IntegerField(default=0)

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_accessed = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'books'
        ordering = ['-updated_at']

class BookCollaborator(models.Model):
    """图书协作者模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    book = models.ForeignKey(Book, on_delete=models.CASCADE)
    user = models.ForeignKey(User, on_delete=models.CASCADE)

    role = models.CharField(
        max_length=20,
        choices=[
            ('owner', '所有者'),
            ('editor', '编辑者'),
            ('reviewer', '审阅者'),
            ('viewer', '查看者')
        ]
    )

    # 权限设置
    permissions = JSONField(default=dict)  # 详细权限配置

    # 邀请信息
    invited_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sent_invitations')
    invited_at = models.DateTimeField(auto_now_add=True)
    accepted_at = models.DateTimeField(null=True, blank=True)

    # 状态
    status = models.CharField(
        max_length=20,
        choices=[
            ('pending', '待接受'),
            ('active', '活跃'),
            ('inactive', '非活跃')
        ],
        default='pending'
    )

    class Meta:
        db_table = 'book_collaborators'
        unique_together = ['book', 'user']

class Chapter(models.Model):
    """章节模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    book = models.ForeignKey(Book, on_delete=models.CASCADE, related_name='chapters')

    # 层次结构支持
    parent = models.ForeignKey('self', null=True, blank=True, on_delete=models.CASCADE, related_name='children')

    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)

    # 排序和显示
    order = models.IntegerField()
    is_visible = models.BooleanField(default=True)

    # 章节设置
    settings = JSONField(default=dict)  # 章节特定设置

    # 统计信息
    page_count = models.IntegerField(default=0)
    word_count = models.IntegerField(default=0)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'chapters'
        ordering = ['order']
        unique_together = ['book', 'order']

class Page(models.Model):
    """页面模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    chapter = models.ForeignKey(Chapter, on_delete=models.CASCADE, related_name='pages')

    title = models.CharField(max_length=200)

    # GrapesJS 数据
    content = JSONField(default=dict)  # GrapesJS 组件数据
    styles = JSONField(default=dict)   # CSS 样式数据
    assets = JSONField(default=list)   # 资源文件引用

    # 页面属性
    order = models.IntegerField()
    is_published = models.BooleanField(default=False)

    # 内容统计
    word_count = models.IntegerField(default=0)
    character_count = models.IntegerField(default=0)

    # 编辑信息
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_pages')
    updated_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='updated_pages')

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # 编辑锁定
    locked_by = models.ForeignKey(User, null=True, blank=True, on_delete=models.SET_NULL, related_name='locked_pages')
    locked_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'pages'
        ordering = ['order']
        unique_together = ['chapter', 'order']

class PageVersion(models.Model):
    """页面版本模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    page = models.ForeignKey(Page, on_delete=models.CASCADE, related_name='versions')

    # 版本信息
    version_number = models.IntegerField()
    parent_version = models.ForeignKey('self', null=True, blank=True, on_delete=models.CASCADE)

    # 版本数据
    content = JSONField()
    styles = JSONField()
    assets = JSONField(default=list)

    # 版本元数据
    comment = models.TextField(blank=True)
    is_major = models.BooleanField(default=False)  # 是否为主要版本
    is_auto = models.BooleanField(default=True)    # 是否为自动保存版本

    # 创建信息
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

    # 统计信息
    word_count = models.IntegerField(default=0)
    changes_summary = JSONField(default=dict)  # 变更摘要

    class Meta:
        db_table = 'page_versions'
        ordering = ['-version_number']
        unique_together = ['page', 'version_number']

class Template(models.Model):
    """模板模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    name = models.CharField(max_length=100)
    description = models.TextField()

    # 分类和标签
    category = models.CharField(max_length=50)
    tags = models.JSONField(default=list)

    # 模板数据
    content = JSONField()  # GrapesJS 组件数据
    styles = JSONField()   # CSS 样式数据
    assets = JSONField(default=list)  # 资源文件

    # 预览和展示
    preview_image = models.ImageField(upload_to='template_previews/')
    thumbnail = models.ImageField(upload_to='template_thumbnails/', blank=True)

    # 访问控制
    is_public = models.BooleanField(default=False)
    is_featured = models.BooleanField(default=False)

    # 统计信息
    download_count = models.IntegerField(default=0)
    rating = models.DecimalField(max_digits=3, decimal_places=2, default=0.00)
    rating_count = models.IntegerField(default=0)

    # 创建信息
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_templates')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'templates'
        ordering = ['-created_at']

class Asset(models.Model):
    """资源文件模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    book = models.ForeignKey(Book, on_delete=models.CASCADE, related_name='assets')

    # 文件信息
    file = models.FileField(upload_to='book_assets/')
    original_name = models.CharField(max_length=200)
    file_type = models.CharField(
        max_length=20,
        choices=[
            ('image', '图片'),
            ('video', '视频'),
            ('audio', '音频'),
            ('document', '文档'),
            ('other', '其他')
        ]
    )
    mime_type = models.CharField(max_length=100)
    file_size = models.BigIntegerField()

    # 图片特有属性
    width = models.IntegerField(null=True, blank=True)
    height = models.IntegerField(null=True, blank=True)

    # 元数据
    alt_text = models.CharField(max_length=200, blank=True)
    description = models.TextField(blank=True)

    # 上传信息
    uploaded_by = models.ForeignKey(User, on_delete=models.CASCADE)
    uploaded_at = models.DateTimeField(auto_now_add=True)

    # 使用统计
    usage_count = models.IntegerField(default=0)
    last_used = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'assets'
        ordering = ['-uploaded_at']

class Comment(models.Model):
    """评论和批注模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    page = models.ForeignKey(Page, on_delete=models.CASCADE, related_name='comments')

    # 评论内容
    content = models.TextField()

    # 位置信息（用于定位评论在页面中的位置）
    position_data = JSONField(default=dict)

    # 评论类型
    comment_type = models.CharField(
        max_length=20,
        choices=[
            ('general', '一般评论'),
            ('suggestion', '建议'),
            ('correction', '修正'),
            ('question', '问题')
        ],
        default='general'
    )

    # 状态管理
    status = models.CharField(
        max_length=20,
        choices=[
            ('open', '开放'),
            ('resolved', '已解决'),
            ('dismissed', '已忽略')
        ],
        default='open'
    )

    # 回复支持
    parent = models.ForeignKey('self', null=True, blank=True, on_delete=models.CASCADE, related_name='replies')

    # 创建信息
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # 解决信息
    resolved_by = models.ForeignKey(User, null=True, blank=True, on_delete=models.SET_NULL, related_name='resolved_comments')
    resolved_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'comments'
        ordering = ['created_at']
```

### 5.2 数据库索引优化

```python
# 在 models.py 中添加索引
class Book(models.Model):
    # ... 字段定义 ...

    class Meta:
        db_table = 'books'
        ordering = ['-updated_at']
        indexes = [
            models.Index(fields=['owner', '-updated_at']),
            models.Index(fields=['status', '-updated_at']),
            models.Index(fields=['-last_accessed']),
        ]

class Page(models.Model):
    # ... 字段定义 ...

    class Meta:
        db_table = 'pages'
        ordering = ['order']
        indexes = [
            models.Index(fields=['chapter', 'order']),
            models.Index(fields=['updated_by', '-updated_at']),
            models.Index(fields=['locked_by', 'locked_at']),
        ]

class PageVersion(models.Model):
    # ... 字段定义 ...

    class Meta:
        db_table = 'page_versions'
        ordering = ['-version_number']
        indexes = [
            models.Index(fields=['page', '-version_number']),
            models.Index(fields=['created_by', '-created_at']),
            models.Index(fields=['is_major', '-created_at']),
        ]
```

### 5.3 数据库迁移脚本

```python
# migrations/0001_initial.py
from django.db import migrations, models
import django.contrib.postgres.fields.jsonb
import uuid

class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        # 创建用户表
        migrations.CreateModel(
            name='User',
            fields=[
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False)),
                ('username', models.CharField(max_length=150, unique=True)),
                ('first_name', models.CharField(blank=True, max_length=150)),
                ('last_name', models.CharField(blank=True, max_length=150)),
                ('email', models.EmailField(blank=True, max_length=254)),
                ('is_staff', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('date_joined', models.DateTimeField(auto_now_add=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('avatar', models.ImageField(blank=True, null=True, upload_to='avatars/')),
                ('subscription_type', models.CharField(choices=[('free', '免费版'), ('pro', '专业版'), ('enterprise', '企业版')], default='free', max_length=20)),
                ('storage_used', models.BigIntegerField(default=0)),
                ('storage_limit', models.BigIntegerField(default=1073741824)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('last_active', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'users',
            },
        ),

        # 创建图书表
        migrations.CreateModel(
            name='Book',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('cover_image', models.ImageField(blank=True, null=True, upload_to='book_covers/')),
                ('settings', django.contrib.postgres.fields.jsonb.JSONField(default=dict)),
                ('status', models.CharField(choices=[('draft', '草稿'), ('review', '审阅中'), ('published', '已发布'), ('archived', '已归档')], default='draft', max_length=20)),
                ('total_pages', models.IntegerField(default=0)),
                ('total_words', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_accessed', models.DateTimeField(auto_now=True)),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='owned_books', to='books.user')),
            ],
            options={
                'db_table': 'books',
                'ordering': ['-updated_at'],
            },
        ),

        # ... 其他表的创建 ...
    ]
```

## 6. API 接口设计

### 6.1 RESTful API 规范

**基础 URL 结构：**
```
https://api.bookEditor.com/v1/
```

**认证方式：**
- JWT Token 认证
- 请求头：`Authorization: Bearer <token>`

**响应格式：**
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2024-01-01T00:00:00Z",
  "request_id": "uuid"
}
```

### 6.2 核心 API 端点

**用户认证 API：**
```python
# views/auth.py
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import authenticate

@api_view(['POST'])
@permission_classes([AllowAny])
def login(request):
    """用户登录"""
    username = request.data.get('username')
    password = request.data.get('password')

    user = authenticate(username=username, password=password)
    if user:
        refresh = RefreshToken.for_user(user)
        return Response({
            'success': True,
            'data': {
                'access_token': str(refresh.access_token),
                'refresh_token': str(refresh),
                'user': {
                    'id': str(user.id),
                    'username': user.username,
                    'email': user.email,
                    'avatar': user.avatar.url if user.avatar else None,
                    'subscription_type': user.subscription_type
                }
            }
        })
    else:
        return Response({
            'success': False,
            'message': '用户名或密码错误'
        }, status=status.HTTP_401_UNAUTHORIZED)

@api_view(['POST'])
@permission_classes([AllowAny])
def register(request):
    """用户注册"""
    serializer = UserRegistrationSerializer(data=request.data)
    if serializer.is_valid():
        user = serializer.save()
        refresh = RefreshToken.for_user(user)
        return Response({
            'success': True,
            'data': {
                'access_token': str(refresh.access_token),
                'refresh_token': str(refresh),
                'user': UserSerializer(user).data
            }
        }, status=status.HTTP_201_CREATED)
    else:
        return Response({
            'success': False,
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def logout(request):
    """用户登出"""
    try:
        refresh_token = request.data.get('refresh_token')
        token = RefreshToken(refresh_token)
        token.blacklist()
        return Response({
            'success': True,
            'message': '登出成功'
        })
    except Exception as e:
        return Response({
            'success': False,
            'message': '登出失败'
        }, status=status.HTTP_400_BAD_REQUEST)
```

**图书管理 API：**
```python
# views/books.py
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.db.models import Q

class BookViewSet(viewsets.ModelViewSet):
    """图书管理视图集"""
    serializer_class = BookSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """获取用户可访问的图书"""
        user = self.request.user
        return Book.objects.filter(
            Q(owner=user) | Q(collaborators=user)
        ).distinct().order_by('-updated_at')

    def create(self, request):
        """创建新图书"""
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            book = serializer.save(owner=request.user)

            # 创建默认章节
            Chapter.objects.create(
                book=book,
                title='第一章',
                order=1
            )

            return Response({
                'success': True,
                'data': BookSerializer(book).data,
                'message': '图书创建成功'
            }, status=status.HTTP_201_CREATED)
        else:
            return Response({
                'success': False,
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def duplicate(self, request, pk=None):
        """复制图书"""
        original_book = self.get_object()

        # 检查权限
        if not self.has_permission(original_book, 'read'):
            return Response({
                'success': False,
                'message': '没有权限复制此图书'
            }, status=status.HTTP_403_FORBIDDEN)

        # 复制图书
        new_book = Book.objects.create(
            title=f"{original_book.title} (副本)",
            description=original_book.description,
            owner=request.user,
            settings=original_book.settings
        )

        # 复制章节和页面
        for chapter in original_book.chapters.all():
            new_chapter = Chapter.objects.create(
                book=new_book,
                title=chapter.title,
                description=chapter.description,
                order=chapter.order,
                settings=chapter.settings
            )

            for page in chapter.pages.all():
                Page.objects.create(
                    chapter=new_chapter,
                    title=page.title,
                    content=page.content,
                    styles=page.styles,
                    assets=page.assets,
                    order=page.order,
                    created_by=request.user,
                    updated_by=request.user
                )

        return Response({
            'success': True,
            'data': BookSerializer(new_book).data,
            'message': '图书复制成功'
        })

    @action(detail=True, methods=['get'])
    def collaborators(self, request, pk=None):
        """获取图书协作者"""
        book = self.get_object()
        collaborators = BookCollaborator.objects.filter(book=book)
        serializer = BookCollaboratorSerializer(collaborators, many=True)

        return Response({
            'success': True,
            'data': serializer.data
        })

    @action(detail=True, methods=['post'])
    def invite_collaborator(self, request, pk=None):
        """邀请协作者"""
        book = self.get_object()

        # 检查权限
        if book.owner != request.user:
            return Response({
                'success': False,
                'message': '只有图书所有者可以邀请协作者'
            }, status=status.HTTP_403_FORBIDDEN)

        email = request.data.get('email')
        role = request.data.get('role', 'viewer')

        try:
            user = User.objects.get(email=email)

            # 检查是否已经是协作者
            if BookCollaborator.objects.filter(book=book, user=user).exists():
                return Response({
                    'success': False,
                    'message': '用户已经是协作者'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 创建协作者邀请
            collaborator = BookCollaborator.objects.create(
                book=book,
                user=user,
                role=role,
                invited_by=request.user,
                status='pending'
            )

            # 发送邀请邮件
            send_collaboration_invitation.delay(collaborator.id)

            return Response({
                'success': True,
                'data': BookCollaboratorSerializer(collaborator).data,
                'message': '邀请已发送'
            })

        except User.DoesNotExist:
            return Response({
                'success': False,
                'message': '用户不存在'
            }, status=status.HTTP_404_NOT_FOUND)

    def has_permission(self, book, action):
        """检查用户权限"""
        user = self.request.user

        if book.owner == user:
            return True

        try:
            collaborator = BookCollaborator.objects.get(book=book, user=user)
            if collaborator.status != 'active':
                return False

            # 根据角色和操作检查权限
            role_permissions = {
                'owner': ['read', 'write', 'delete', 'manage'],
                'editor': ['read', 'write'],
                'reviewer': ['read', 'comment'],
                'viewer': ['read']
            }

            return action in role_permissions.get(collaborator.role, [])
        except BookCollaborator.DoesNotExist:
            return False
```

**页面管理 API：**
```python
# views/pages.py
class PageViewSet(viewsets.ModelViewSet):
    """页面管理视图集"""
    serializer_class = PageSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        chapter_id = self.kwargs.get('chapter_pk')
        return Page.objects.filter(chapter_id=chapter_id).order_by('order')

    def create(self, request, chapter_pk=None):
        """创建新页面"""
        try:
            chapter = Chapter.objects.get(id=chapter_pk)

            # 检查权限
            if not self.has_write_permission(chapter.book):
                return Response({
                    'success': False,
                    'message': '没有权限创建页面'
                }, status=status.HTTP_403_FORBIDDEN)

            # 获取下一个排序号
            last_page = chapter.pages.order_by('-order').first()
            next_order = (last_page.order + 1) if last_page else 1

            serializer = self.get_serializer(data=request.data)
            if serializer.is_valid():
                page = serializer.save(
                    chapter=chapter,
                    order=next_order,
                    created_by=request.user,
                    updated_by=request.user
                )

                # 创建初始版本
                PageVersion.objects.create(
                    page=page,
                    version_number=1,
                    content=page.content,
                    styles=page.styles,
                    assets=page.assets,
                    created_by=request.user,
                    comment='初始版本',
                    is_major=True,
                    is_auto=False
                )

                return Response({
                    'success': True,
                    'data': PageSerializer(page).data,
                    'message': '页面创建成功'
                }, status=status.HTTP_201_CREATED)
            else:
                return Response({
                    'success': False,
                    'errors': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)

        except Chapter.DoesNotExist:
            return Response({
                'success': False,
                'message': '章节不存在'
            }, status=status.HTTP_404_NOT_FOUND)

    def update(self, request, pk=None, chapter_pk=None):
        """更新页面"""
        page = self.get_object()

        # 检查编辑锁
        if page.locked_by and page.locked_by != request.user:
            return Response({
                'success': False,
                'message': f'页面正在被 {page.locked_by.username} 编辑',
                'locked_by': {
                    'id': str(page.locked_by.id),
                    'username': page.locked_by.username
                }
            }, status=status.HTTP_423_LOCKED)

        # 保存当前版本
        if self.should_create_version(page, request.data):
            self.create_version(page, request.user)

        serializer = self.get_serializer(page, data=request.data, partial=True)
        if serializer.is_valid():
            updated_page = serializer.save(updated_by=request.user)

            # 更新字数统计
            updated_page.word_count = self.calculate_word_count(updated_page.content)
            updated_page.save()

            # 广播变更给协作者
            broadcast_page_change.delay(
                page_id=str(updated_page.id),
                user_id=str(request.user.id),
                changes=request.data
            )

            return Response({
                'success': True,
                'data': PageSerializer(updated_page).data,
                'message': '页面更新成功'
            })
        else:
            return Response({
                'success': False,
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def acquire_lock(self, request, pk=None, chapter_pk=None):
        """获取编辑锁"""
        page = self.get_object()

        # 检查是否已被锁定
        if page.locked_by and page.locked_by != request.user:
            lock_duration = timezone.now() - page.locked_at
            if lock_duration.total_seconds() < 300:  # 5分钟锁定时间
                return Response({
                    'success': False,
                    'message': '页面正在被其他用户编辑',
                    'locked_by': {
                        'id': str(page.locked_by.id),
                        'username': page.locked_by.username
                    }
                }, status=status.HTTP_423_LOCKED)

        # 获取锁定
        page.locked_by = request.user
        page.locked_at = timezone.now()
        page.save()

        return Response({
            'success': True,
            'message': '获取编辑锁成功'
        })

    @action(detail=True, methods=['post'])
    def release_lock(self, request, pk=None, chapter_pk=None):
        """释放编辑锁"""
        page = self.get_object()

        if page.locked_by == request.user:
            page.locked_by = None
            page.locked_at = None
            page.save()

            return Response({
                'success': True,
                'message': '编辑锁已释放'
            })
        else:
            return Response({
                'success': False,
                'message': '无权释放此编辑锁'
            }, status=status.HTTP_403_FORBIDDEN)

    @action(detail=True, methods=['get'])
    def versions(self, request, pk=None, chapter_pk=None):
        """获取页面版本历史"""
        page = self.get_object()
        versions = page.versions.all()[:20]  # 最近20个版本

        serializer = PageVersionSerializer(versions, many=True)
        return Response({
            'success': True,
            'data': serializer.data
        })

    @action(detail=True, methods=['post'])
    def restore_version(self, request, pk=None, chapter_pk=None):
        """恢复到指定版本"""
        page = self.get_object()
        version_id = request.data.get('version_id')

        try:
            version = PageVersion.objects.get(id=version_id, page=page)

            # 创建恢复版本
            self.create_version(page, request.user, f'恢复到版本 {version.version_number}')

            # 恢复内容
            page.content = version.content
            page.styles = version.styles
            page.assets = version.assets
            page.updated_by = request.user
            page.save()

            return Response({
                'success': True,
                'data': PageSerializer(page).data,
                'message': f'已恢复到版本 {version.version_number}'
            })

        except PageVersion.DoesNotExist:
            return Response({
                'success': False,
                'message': '版本不存在'
            }, status=status.HTTP_404_NOT_FOUND)

    def should_create_version(self, page, new_data):
        """判断是否需要创建新版本"""
        # 如果内容有重大变更，创建版本
        if 'content' in new_data:
            old_content = json.dumps(page.content, sort_keys=True)
            new_content = json.dumps(new_data['content'], sort_keys=True)

            # 计算变更百分比
            diff_ratio = difflib.SequenceMatcher(None, old_content, new_content).ratio()
            return diff_ratio < 0.9  # 如果变更超过10%，创建版本

        return False

    def create_version(self, page, user, comment='自动保存'):
        """创建页面版本"""
        last_version = page.versions.first()
        version_number = (last_version.version_number + 1) if last_version else 1

        PageVersion.objects.create(
            page=page,
            version_number=version_number,
            content=page.content,
            styles=page.styles,
            assets=page.assets,
            created_by=user,
            comment=comment,
            is_auto=True
        )

    def calculate_word_count(self, content):
        """计算字数"""
        # 简化的字数计算，实际应该解析GrapesJS内容
        text_content = self.extract_text_from_content(content)
        return len(text_content.split())

    def has_write_permission(self, book):
        """检查写权限"""
        user = self.request.user
        if book.owner == user:
            return True

        try:
            collaborator = BookCollaborator.objects.get(book=book, user=user)
            return collaborator.role in ['editor'] and collaborator.status == 'active'
        except BookCollaborator.DoesNotExist:
            return False
```

### 6.3 WebSocket 实时协作接口

**Django Channels 消费者：**
```python
# consumers.py
import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth.models import AnonymousUser

class BookCollaborationConsumer(AsyncWebsocketConsumer):
    """图书协作WebSocket消费者"""

    async def connect(self):
        self.book_id = self.scope['url_route']['kwargs']['book_id']
        self.room_group_name = f'book_{self.book_id}'
        self.user = self.scope['user']

        if isinstance(self.user, AnonymousUser):
            await self.close()
            return

        # 检查用户权限
        has_permission = await self.check_book_permission()
        if not has_permission:
            await self.close()
            return

        # 加入房间组
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )

        await self.accept()

        # 通知其他用户有新用户加入
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'user_join',
                'user_id': str(self.user.id),
                'username': self.user.username,
                'avatar': self.user.avatar.url if self.user.avatar else None
            }
        )

    async def disconnect(self, close_code):
        # 通知其他用户有用户离开
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'user_leave',
                'user_id': str(self.user.id),
                'username': self.user.username
            }
        )

        # 离开房间组
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )

    async def receive(self, text_data):
        """接收客户端消息"""
        try:
            data = json.loads(text_data)
            message_type = data.get('type')

            # 根据消息类型处理
            if message_type == 'cursor_move':
                await self.handle_cursor_move(data)
            elif message_type == 'content_change':
                await self.handle_content_change(data)
            elif message_type == 'selection_change':
                await self.handle_selection_change(data)
            elif message_type == 'lock_acquire':
                await self.handle_lock_acquire(data)
            elif message_type == 'lock_release':
                await self.handle_lock_release(data)
            elif message_type == 'comment_add':
                await self.handle_comment_add(data)

        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': '无效的JSON格式'
            }))

    async def handle_cursor_move(self, data):
        """处理光标移动"""
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'cursor_move',
                'user_id': str(self.user.id),
                'username': self.user.username,
                'page_id': data.get('page_id'),
                'position': data.get('position'),
                'timestamp': data.get('timestamp')
            }
        )

    async def handle_content_change(self, data):
        """处理内容变更"""
        page_id = data.get('page_id')
        changes = data.get('changes')

        # 保存变更到数据库
        await self.save_content_change(page_id, changes)

        # 广播给其他用户
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'content_change',
                'user_id': str(self.user.id),
                'username': self.user.username,
                'page_id': page_id,
                'changes': changes,
                'timestamp': data.get('timestamp')
            }
        )

    async def handle_lock_acquire(self, data):
        """处理获取编辑锁"""
        page_id = data.get('page_id')

        # 尝试获取锁
        lock_acquired = await self.acquire_page_lock(page_id)

        if lock_acquired:
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'lock_acquire',
                    'user_id': str(self.user.id),
                    'username': self.user.username,
                    'page_id': page_id,
                    'timestamp': data.get('timestamp')
                }
            )
        else:
            await self.send(text_data=json.dumps({
                'type': 'lock_failed',
                'page_id': page_id,
                'message': '无法获取编辑锁，页面可能正在被其他用户编辑'
            }))

    async def handle_lock_release(self, data):
        """处理释放编辑锁"""
        page_id = data.get('page_id')

        # 释放锁
        await self.release_page_lock(page_id)

        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'lock_release',
                'user_id': str(self.user.id),
                'username': self.user.username,
                'page_id': page_id,
                'timestamp': data.get('timestamp')
            }
        )

    # WebSocket 事件处理器
    async def user_join(self, event):
        """用户加入事件"""
        if event['user_id'] != str(self.user.id):  # 不发送给自己
            await self.send(text_data=json.dumps({
                'type': 'user_join',
                'user_id': event['user_id'],
                'username': event['username'],
                'avatar': event.get('avatar')
            }))

    async def user_leave(self, event):
        """用户离开事件"""
        if event['user_id'] != str(self.user.id):
            await self.send(text_data=json.dumps({
                'type': 'user_leave',
                'user_id': event['user_id'],
                'username': event['username']
            }))

    async def cursor_move(self, event):
        """光标移动事件"""
        if event['user_id'] != str(self.user.id):
            await self.send(text_data=json.dumps({
                'type': 'cursor_move',
                'user_id': event['user_id'],
                'username': event['username'],
                'page_id': event['page_id'],
                'position': event['position'],
                'timestamp': event['timestamp']
            }))

    async def content_change(self, event):
        """内容变更事件"""
        if event['user_id'] != str(self.user.id):
            await self.send(text_data=json.dumps({
                'type': 'content_change',
                'user_id': event['user_id'],
                'username': event['username'],
                'page_id': event['page_id'],
                'changes': event['changes'],
                'timestamp': event['timestamp']
            }))

    async def lock_acquire(self, event):
        """获取锁事件"""
        await self.send(text_data=json.dumps({
            'type': 'lock_acquire',
            'user_id': event['user_id'],
            'username': event['username'],
            'page_id': event['page_id'],
            'timestamp': event['timestamp']
        }))

    async def lock_release(self, event):
        """释放锁事件"""
        await self.send(text_data=json.dumps({
            'type': 'lock_release',
            'user_id': event['user_id'],
            'username': event['username'],
            'page_id': event['page_id'],
            'timestamp': event['timestamp']
        }))

    # 数据库操作方法
    @database_sync_to_async
    def check_book_permission(self):
        """检查图书访问权限"""
        try:
            from .models import Book, BookCollaborator
            book = Book.objects.get(id=self.book_id)

            if book.owner == self.user:
                return True

            collaborator = BookCollaborator.objects.get(
                book=book,
                user=self.user,
                status='active'
            )
            return True
        except:
            return False

    @database_sync_to_async
    def save_content_change(self, page_id, changes):
        """保存内容变更"""
        try:
            from .models import Page
            page = Page.objects.get(id=page_id)

            # 更新页面内容
            if 'content' in changes:
                page.content = changes['content']
            if 'styles' in changes:
                page.styles = changes['styles']

            page.updated_by = self.user
            page.save()

            return True
        except:
            return False

    @database_sync_to_async
    def acquire_page_lock(self, page_id):
        """获取页面编辑锁"""
        try:
            from .models import Page
            from django.utils import timezone

            page = Page.objects.get(id=page_id)

            # 检查是否已被锁定
            if page.locked_by and page.locked_by != self.user:
                lock_duration = timezone.now() - page.locked_at
                if lock_duration.total_seconds() < 300:  # 5分钟锁定时间
                    return False

            # 获取锁
            page.locked_by = self.user
            page.locked_at = timezone.now()
            page.save()

            return True
        except:
            return False

    @database_sync_to_async
    def release_page_lock(self, page_id):
        """释放页面编辑锁"""
        try:
            from .models import Page

            page = Page.objects.get(id=page_id, locked_by=self.user)
            page.locked_by = None
            page.locked_at = None
            page.save()

            return True
        except:
            return False
```

## 7. Electron 桌面应用方案

### 7.1 Electron 架构设计

**主进程 (Main Process) 架构：**
```javascript
// src/main/main.js
const { app, BrowserWindow, Menu, ipcMain, dialog, shell } = require('electron');
const { autoUpdater } = require('electron-updater');
const path = require('path');
const isDev = process.env.NODE_ENV === 'development';

class MainApplication {
  constructor() {
    this.mainWindow = null;
    this.isQuitting = false;

    this.setupApp();
    this.setupAutoUpdater();
    this.setupIPC();
  }

  setupApp() {
    // 应用准备就绪
    app.whenReady().then(() => {
      this.createMainWindow();
      this.createMenu();

      app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
          this.createMainWindow();
        }
      });
    });

    // 所有窗口关闭
    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

    // 应用退出前
    app.on('before-quit', () => {
      this.isQuitting = true;
    });
  }

  createMainWindow() {
    this.mainWindow = new BrowserWindow({
      width: 1400,
      height: 900,
      minWidth: 1200,
      minHeight: 700,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        preload: path.join(__dirname, 'preload.js')
      },
      titleBarStyle: 'hiddenInset', // macOS 样式
      show: false // 先隐藏，加载完成后显示
    });

    // 加载应用
    const startUrl = isDev
      ? 'http://localhost:3000'
      : `file://${path.join(__dirname, '../build/index.html')}`;

    this.mainWindow.loadURL(startUrl);

    // 窗口准备显示
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow.show();

      if (isDev) {
        this.mainWindow.webContents.openDevTools();
      }
    });

    // 窗口关闭处理
    this.mainWindow.on('close', (event) => {
      if (!this.isQuitting && process.platform === 'darwin') {
        event.preventDefault();
        this.mainWindow.hide();
      }
    });

    // 处理外部链接
    this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
      shell.openExternal(url);
      return { action: 'deny' };
    });
  }

  createMenu() {
    const template = [
      {
        label: '文件',
        submenu: [
          {
            label: '新建图书',
            accelerator: 'CmdOrCtrl+N',
            click: () => this.sendToRenderer('menu:new-book')
          },
          {
            label: '打开图书',
            accelerator: 'CmdOrCtrl+O',
            click: () => this.handleOpenBook()
          },
          {
            label: '保存',
            accelerator: 'CmdOrCtrl+S',
            click: () => this.sendToRenderer('menu:save')
          },
          {
            label: '另存为',
            accelerator: 'CmdOrCtrl+Shift+S',
            click: () => this.handleSaveAs()
          },
          { type: 'separator' },
          {
            label: '导入',
            submenu: [
              {
                label: '从 Word 导入',
                click: () => this.handleImportWord()
              },
              {
                label: '从 Markdown 导入',
                click: () => this.handleImportMarkdown()
              }
            ]
          },
          {
            label: '导出',
            submenu: [
              {
                label: '导出为 PDF',
                click: () => this.sendToRenderer('menu:export-pdf')
              },
              {
                label: '导出为 EPUB',
                click: () => this.sendToRenderer('menu:export-epub')
              },
              {
                label: '导出为 HTML',
                click: () => this.sendToRenderer('menu:export-html')
              }
            ]
          },
          { type: 'separator' },
          {
            label: '打印',
            accelerator: 'CmdOrCtrl+P',
            click: () => this.handlePrint()
          },
          { type: 'separator' },
          {
            label: '退出',
            accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
            click: () => {
              this.isQuitting = true;
              app.quit();
            }
          }
        ]
      },
      {
        label: '编辑',
        submenu: [
          {
            label: '撤销',
            accelerator: 'CmdOrCtrl+Z',
            click: () => this.sendToRenderer('menu:undo')
          },
          {
            label: '重做',
            accelerator: 'CmdOrCtrl+Y',
            click: () => this.sendToRenderer('menu:redo')
          },
          { type: 'separator' },
          {
            label: '复制',
            accelerator: 'CmdOrCtrl+C',
            role: 'copy'
          },
          {
            label: '粘贴',
            accelerator: 'CmdOrCtrl+V',
            role: 'paste'
          },
          {
            label: '全选',
            accelerator: 'CmdOrCtrl+A',
            role: 'selectall'
          },
          { type: 'separator' },
          {
            label: '查找',
            accelerator: 'CmdOrCtrl+F',
            click: () => this.sendToRenderer('menu:find')
          },
          {
            label: '替换',
            accelerator: 'CmdOrCtrl+H',
            click: () => this.sendToRenderer('menu:replace')
          }
        ]
      },
      {
        label: '视图',
        submenu: [
          {
            label: '缩放',
            submenu: [
              {
                label: '放大',
                accelerator: 'CmdOrCtrl+Plus',
                click: () => this.sendToRenderer('menu:zoom-in')
              },
              {
                label: '缩小',
                accelerator: 'CmdOrCtrl+-',
                click: () => this.sendToRenderer('menu:zoom-out')
              },
              {
                label: '重置缩放',
                accelerator: 'CmdOrCtrl+0',
                click: () => this.sendToRenderer('menu:zoom-reset')
              }
            ]
          },
          { type: 'separator' },
          {
            label: '全屏',
            accelerator: 'F11',
            click: () => {
              const isFullScreen = this.mainWindow.isFullScreen();
              this.mainWindow.setFullScreen(!isFullScreen);
            }
          },
          {
            label: '开发者工具',
            accelerator: 'F12',
            click: () => {
              this.mainWindow.webContents.toggleDevTools();
            }
          }
        ]
      },
      {
        label: '工具',
        submenu: [
          {
            label: 'AI 助手',
            accelerator: 'CmdOrCtrl+Shift+A',
            click: () => this.sendToRenderer('menu:ai-assistant')
          },
          {
            label: '拼写检查',
            click: () => this.sendToRenderer('menu:spell-check')
          },
          {
            label: '字数统计',
            click: () => this.sendToRenderer('menu:word-count')
          },
          { type: 'separator' },
          {
            label: '偏好设置',
            accelerator: 'CmdOrCtrl+,',
            click: () => this.sendToRenderer('menu:preferences')
          }
        ]
      },
      {
        label: '帮助',
        submenu: [
          {
            label: '用户手册',
            click: () => shell.openExternal('https://docs.bookeditor.com')
          },
          {
            label: '快捷键',
            click: () => this.sendToRenderer('menu:shortcuts')
          },
          { type: 'separator' },
          {
            label: '检查更新',
            click: () => autoUpdater.checkForUpdatesAndNotify()
          },
          {
            label: '关于',
            click: () => this.showAboutDialog()
          }
        ]
      }
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
  }

  setupIPC() {
    // 文件操作
    ipcMain.handle('file:save-book', this.handleSaveBook.bind(this));
    ipcMain.handle('file:open-book', this.handleOpenBookDialog.bind(this));
    ipcMain.handle('file:export-pdf', this.handleExportPDF.bind(this));
    ipcMain.handle('file:import-word', this.handleImportWordDialog.bind(this));

    // 应用操作
    ipcMain.handle('app:get-version', () => app.getVersion());
    ipcMain.handle('app:show-message-box', this.handleShowMessageBox.bind(this));
    ipcMain.handle('app:show-save-dialog', this.handleShowSaveDialog.bind(this));
    ipcMain.handle('app:show-open-dialog', this.handleShowOpenDialog.bind(this));

    // 窗口操作
    ipcMain.handle('window:minimize', () => this.mainWindow.minimize());
    ipcMain.handle('window:maximize', () => {
      if (this.mainWindow.isMaximized()) {
        this.mainWindow.unmaximize();
      } else {
        this.mainWindow.maximize();
      }
    });
    ipcMain.handle('window:close', () => this.mainWindow.close());

    // 系统操作
    ipcMain.handle('system:open-external', (event, url) => shell.openExternal(url));
    ipcMain.handle('system:show-item-in-folder', (event, path) => shell.showItemInFolder(path));
  }

  setupAutoUpdater() {
    if (!isDev) {
      autoUpdater.checkForUpdatesAndNotify();

      autoUpdater.on('update-available', () => {
        dialog.showMessageBox(this.mainWindow, {
          type: 'info',
          title: '更新可用',
          message: '发现新版本，正在下载...',
          buttons: ['确定']
        });
      });

      autoUpdater.on('update-downloaded', () => {
        dialog.showMessageBox(this.mainWindow, {
          type: 'info',
          title: '更新就绪',
          message: '更新已下载完成，重启应用以应用更新。',
          buttons: ['立即重启', '稍后重启']
        }).then((result) => {
          if (result.response === 0) {
            autoUpdater.quitAndInstall();
          }
        });
      });
    }
  }

  // 工具方法
  sendToRenderer(channel, ...args) {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send(channel, ...args);
    }
  }

  async handleSaveBook(event, bookData) {
    try {
      const result = await dialog.showSaveDialog(this.mainWindow, {
        title: '保存图书',
        defaultPath: `${bookData.title}.book`,
        filters: [
          { name: '图书文件', extensions: ['book'] },
          { name: 'JSON 文件', extensions: ['json'] }
        ]
      });

      if (!result.canceled) {
        const fs = require('fs').promises;
        await fs.writeFile(result.filePath, JSON.stringify(bookData, null, 2));
        return { success: true, path: result.filePath };
      }

      return { success: false, cancelled: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async handleOpenBookDialog() {
    try {
      const result = await dialog.showOpenDialog(this.mainWindow, {
        title: '打开图书',
        properties: ['openFile'],
        filters: [
          { name: '图书文件', extensions: ['book', 'json'] },
          { name: '所有文件', extensions: ['*'] }
        ]
      });

      if (!result.canceled && result.filePaths.length > 0) {
        const fs = require('fs').promises;
        const data = await fs.readFile(result.filePaths[0], 'utf8');
        const bookData = JSON.parse(data);
        return { success: true, data: bookData, path: result.filePaths[0] };
      }

      return { success: false, cancelled: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async handleExportPDF(event, bookData) {
    try {
      const result = await dialog.showSaveDialog(this.mainWindow, {
        title: '导出 PDF',
        defaultPath: `${bookData.title}.pdf`,
        filters: [
          { name: 'PDF 文件', extensions: ['pdf'] }
        ]
      });

      if (!result.canceled) {
        // 使用 Puppeteer 或其他 PDF 生成库
        const pdfBuffer = await this.generatePDF(bookData);
        const fs = require('fs').promises;
        await fs.writeFile(result.filePath, pdfBuffer);
        return { success: true, path: result.filePath };
      }

      return { success: false, cancelled: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async generatePDF(bookData) {
    // PDF 生成逻辑
    const puppeteer = require('puppeteer');

    const browser = await puppeteer.launch();
    const page = await browser.newPage();

    // 生成 HTML 内容
    const htmlContent = this.generateHTMLFromBook(bookData);
    await page.setContent(htmlContent);

    // 生成 PDF
    const pdfBuffer = await page.pdf({
      format: 'A4',
      margin: {
        top: '20mm',
        right: '20mm',
        bottom: '20mm',
        left: '20mm'
      },
      printBackground: true
    });

    await browser.close();
    return pdfBuffer;
  }

  showAboutDialog() {
    dialog.showMessageBox(this.mainWindow, {
      type: 'info',
      title: '关于图书编辑器',
      message: '图书编辑器',
      detail: `版本: ${app.getVersion()}\n基于 Electron 和 GrapesJS 构建`,
      buttons: ['确定']
    });
  }
}

// 启动应用
new MainApplication();
```

**预加载脚本 (Preload Script)：**
```javascript
// src/main/preload.js
const { contextBridge, ipcRenderer } = require('electron');

// 暴露安全的 API 给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 文件操作
  saveBook: (bookData) => ipcRenderer.invoke('file:save-book', bookData),
  openBook: () => ipcRenderer.invoke('file:open-book'),
  exportPDF: (bookData) => ipcRenderer.invoke('file:export-pdf', bookData),
  exportEPUB: (bookData) => ipcRenderer.invoke('file:export-epub', bookData),
  importWord: () => ipcRenderer.invoke('file:import-word'),

  // 应用操作
  getVersion: () => ipcRenderer.invoke('app:get-version'),
  showMessageBox: (options) => ipcRenderer.invoke('app:show-message-box', options),
  showSaveDialog: (options) => ipcRenderer.invoke('app:show-save-dialog', options),
  showOpenDialog: (options) => ipcRenderer.invoke('app:show-open-dialog', options),

  // 窗口操作
  minimizeWindow: () => ipcRenderer.invoke('window:minimize'),
  maximizeWindow: () => ipcRenderer.invoke('window:maximize'),
  closeWindow: () => ipcRenderer.invoke('window:close'),

  // 系统操作
  openExternal: (url) => ipcRenderer.invoke('system:open-external', url),
  showItemInFolder: (path) => ipcRenderer.invoke('system:show-item-in-folder', path),

  // 事件监听
  onMenuAction: (callback) => {
    const channels = [
      'menu:new-book',
      'menu:save',
      'menu:undo',
      'menu:redo',
      'menu:zoom-in',
      'menu:zoom-out',
      'menu:zoom-reset',
      'menu:ai-assistant',
      'menu:spell-check',
      'menu:word-count',
      'menu:preferences',
      'menu:shortcuts'
    ];

    channels.forEach(channel => {
      ipcRenderer.on(channel, callback);
    });

    // 返回清理函数
    return () => {
      channels.forEach(channel => {
        ipcRenderer.removeListener(channel, callback);
      });
    };
  },

  // 拖拽文件处理
  onFileDrop: (callback) => {
    ipcRenderer.on('file:drop', callback);
    return () => ipcRenderer.removeListener('file:drop', callback);
  }
});

// 处理拖拽文件
document.addEventListener('DOMContentLoaded', () => {
  document.addEventListener('dragover', (e) => {
    e.preventDefault();
    e.stopPropagation();
  });

  document.addEventListener('drop', (e) => {
    e.preventDefault();
    e.stopPropagation();

    const files = Array.from(e.dataTransfer.files);
    const fileData = files.map(file => ({
      name: file.name,
      path: file.path,
      size: file.size,
      type: file.type
    }));

    ipcRenderer.send('file:drop', fileData);
  });
});
```

### 7.2 React 应用集成

**主应用组件：**
```typescript
// src/renderer/App.tsx
import React, { useEffect, useState } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider, message } from 'antd';
import zhCN from 'antd/locale/zh_CN';

import { useElectronAPI } from './hooks/useElectronAPI';
import { useBookStore } from './stores/bookStore';
import MainLayout from './components/Layout/MainLayout';
import BookEditor from './components/Editor/BookEditor';
import BookList from './components/Book/BookList';
import Settings from './components/Settings/Settings';

const App: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const { electronAPI } = useElectronAPI();
  const { loadBooks, createBook, saveBook, currentBook } = useBookStore();

  useEffect(() => {
    // 初始化应用
    initializeApp();

    // 设置菜单事件监听
    const cleanup = electronAPI?.onMenuAction(handleMenuAction);

    return cleanup;
  }, []);

  const initializeApp = async () => {
    try {
      // 加载用户图书列表
      await loadBooks();
      setIsLoading(false);
    } catch (error) {
      console.error('应用初始化失败:', error);
      message.error('应用初始化失败');
      setIsLoading(false);
    }
  };

  const handleMenuAction = (event: any, action: string) => {
    switch (action) {
      case 'menu:new-book':
        handleNewBook();
        break;
      case 'menu:save':
        handleSave();
        break;
      case 'menu:undo':
        handleUndo();
        break;
      case 'menu:redo':
        handleRedo();
        break;
      case 'menu:zoom-in':
        handleZoomIn();
        break;
      case 'menu:zoom-out':
        handleZoomOut();
        break;
      case 'menu:zoom-reset':
        handleZoomReset();
        break;
      case 'menu:ai-assistant':
        handleAIAssistant();
        break;
      case 'menu:spell-check':
        handleSpellCheck();
        break;
      case 'menu:word-count':
        handleWordCount();
        break;
      case 'menu:preferences':
        handlePreferences();
        break;
      default:
        console.log('未处理的菜单操作:', action);
    }
  };

  const handleNewBook = async () => {
    try {
      const newBook = await createBook({
        title: '新图书',
        description: '',
        settings: {
          pageSize: 'A4',
          margins: { top: 20, right: 20, bottom: 20, left: 20 },
          fonts: {
            primary: 'Times New Roman',
            secondary: 'Arial',
            monospace: 'Courier New'
          },
          theme: 'default',
          language: 'zh-CN'
        }
      });

      message.success('新图书创建成功');
    } catch (error) {
      message.error('创建图书失败');
    }
  };

  const handleSave = async () => {
    if (!currentBook) {
      message.warning('没有打开的图书');
      return;
    }

    try {
      if (electronAPI) {
        const result = await electronAPI.saveBook(currentBook);
        if (result.success) {
          message.success('图书保存成功');
        } else if (!result.cancelled) {
          message.error(`保存失败: ${result.error}`);
        }
      } else {
        // 在线保存
        await saveBook(currentBook);
        message.success('图书保存成功');
      }
    } catch (error) {
      message.error('保存图书失败');
    }
  };

  const handleUndo = () => {
    // 实现撤销逻辑
    document.dispatchEvent(new CustomEvent('editor:undo'));
  };

  const handleRedo = () => {
    // 实现重做逻辑
    document.dispatchEvent(new CustomEvent('editor:redo'));
  };

  const handleZoomIn = () => {
    document.dispatchEvent(new CustomEvent('editor:zoom', { detail: { action: 'in' } }));
  };

  const handleZoomOut = () => {
    document.dispatchEvent(new CustomEvent('editor:zoom', { detail: { action: 'out' } }));
  };

  const handleZoomReset = () => {
    document.dispatchEvent(new CustomEvent('editor:zoom', { detail: { action: 'reset' } }));
  };

  const handleAIAssistant = () => {
    document.dispatchEvent(new CustomEvent('editor:ai-assistant'));
  };

  const handleSpellCheck = () => {
    document.dispatchEvent(new CustomEvent('editor:spell-check'));
  };

  const handleWordCount = () => {
    document.dispatchEvent(new CustomEvent('editor:word-count'));
  };

  const handlePreferences = () => {
    // 打开设置页面
    window.location.hash = '/settings';
  };

  if (isLoading) {
    return (
      <div className="app-loading">
        <div className="loading-spinner">加载中...</div>
      </div>
    );
  }

  return (
    <ConfigProvider locale={zhCN}>
      <Router>
        <MainLayout>
          <Routes>
            <Route path="/" element={<BookList />} />
            <Route path="/editor/:bookId" element={<BookEditor />} />
            <Route path="/settings" element={<Settings />} />
          </Routes>
        </MainLayout>
      </Router>
    </ConfigProvider>
  );
};

export default App;
```

**Electron API Hook：**
```typescript
// src/renderer/hooks/useElectronAPI.ts
import { useEffect, useState } from 'react';

interface ElectronAPI {
  saveBook: (bookData: any) => Promise<{ success: boolean; path?: string; error?: string; cancelled?: boolean }>;
  openBook: () => Promise<{ success: boolean; data?: any; path?: string; error?: string; cancelled?: boolean }>;
  exportPDF: (bookData: any) => Promise<{ success: boolean; path?: string; error?: string; cancelled?: boolean }>;
  exportEPUB: (bookData: any) => Promise<{ success: boolean; path?: string; error?: string; cancelled?: boolean }>;
  importWord: () => Promise<{ success: boolean; data?: any; error?: string; cancelled?: boolean }>;
  getVersion: () => Promise<string>;
  showMessageBox: (options: any) => Promise<any>;
  showSaveDialog: (options: any) => Promise<any>;
  showOpenDialog: (options: any) => Promise<any>;
  minimizeWindow: () => Promise<void>;
  maximizeWindow: () => Promise<void>;
  closeWindow: () => Promise<void>;
  openExternal: (url: string) => Promise<void>;
  showItemInFolder: (path: string) => Promise<void>;
  onMenuAction: (callback: (event: any, action: string) => void) => () => void;
  onFileDrop: (callback: (event: any, files: any[]) => void) => () => void;
}

export const useElectronAPI = () => {
  const [electronAPI, setElectronAPI] = useState<ElectronAPI | null>(null);
  const [isElectron, setIsElectron] = useState(false);

  useEffect(() => {
    // 检查是否在 Electron 环境中
    if (window.electronAPI) {
      setElectronAPI(window.electronAPI);
      setIsElectron(true);
    } else {
      setIsElectron(false);
    }
  }, []);

  return {
    electronAPI,
    isElectron
  };
};

// 扩展 Window 接口
declare global {
  interface Window {
    electronAPI?: ElectronAPI;
  }
}
```

### 7.3 桌面端特有功能

**本地文件处理：**
```typescript
// src/renderer/services/FileService.ts
import { useElectronAPI } from '../hooks/useElectronAPI';

export class FileService {
  private electronAPI: any;

  constructor() {
    const { electronAPI } = useElectronAPI();
    this.electronAPI = electronAPI;
  }

  // 导入 Word 文档
  async importWordDocument(): Promise<any> {
    if (!this.electronAPI) {
      throw new Error('此功能仅在桌面版中可用');
    }

    try {
      const result = await this.electronAPI.importWord();
      if (result.success) {
        return this.parseWordDocument(result.data);
      } else if (!result.cancelled) {
        throw new Error(result.error);
      }
      return null;
    } catch (error) {
      console.error('导入 Word 文档失败:', error);
      throw error;
    }
  }

  // 解析 Word 文档
  private parseWordDocument(wordData: any): any {
    // 使用 mammoth.js 或类似库解析 Word 文档
    // 转换为 GrapesJS 组件格式
    return {
      title: wordData.title || '导入的文档',
      chapters: [
        {
          title: '第一章',
          pages: [
            {
              title: '第一页',
              content: this.convertWordToGrapesJS(wordData.content),
              styles: {}
            }
          ]
        }
      ]
    };
  }

  // 转换 Word 内容为 GrapesJS 格式
  private convertWordToGrapesJS(content: string): any {
    // 简化的转换逻辑
    const components = [];

    // 解析段落
    const paragraphs = content.split('\n\n');
    paragraphs.forEach((paragraph, index) => {
      if (paragraph.trim()) {
        components.push({
          type: 'text',
          content: paragraph.trim(),
          style: {
            'margin-bottom': '16px'
          }
        });
      }
    });

    return components;
  }

  // 导出为 PDF
  async exportToPDF(bookData: any): Promise<string | null> {
    if (!this.electronAPI) {
      throw new Error('此功能仅在桌面版中可用');
    }

    try {
      const result = await this.electronAPI.exportPDF(bookData);
      if (result.success) {
        return result.path;
      } else if (!result.cancelled) {
        throw new Error(result.error);
      }
      return null;
    } catch (error) {
      console.error('导出 PDF 失败:', error);
      throw error;
    }
  }

  // 导出为 EPUB
  async exportToEPUB(bookData: any): Promise<string | null> {
    if (!this.electronAPI) {
      throw new Error('此功能仅在桌面版中可用');
    }

    try {
      const result = await this.electronAPI.exportEPUB(bookData);
      if (result.success) {
        return result.path;
      } else if (!result.cancelled) {
        throw new Error(result.error);
      }
      return null;
    } catch (error) {
      console.error('导出 EPUB 失败:', error);
      throw error;
    }
  }

  // 处理拖拽文件
  handleFileDrop(files: File[]): Promise<any[]> {
    return Promise.all(files.map(file => this.processDroppedFile(file)));
  }

  private async processDroppedFile(file: File): Promise<any> {
    const fileExtension = file.name.split('.').pop()?.toLowerCase();

    switch (fileExtension) {
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'svg':
        return this.processImageFile(file);
      case 'docx':
      case 'doc':
        return this.processWordFile(file);
      case 'md':
        return this.processMarkdownFile(file);
      default:
        throw new Error(`不支持的文件类型: ${fileExtension}`);
    }
  }

  private async processImageFile(file: File): Promise<any> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        resolve({
          type: 'image',
          name: file.name,
          data: e.target?.result,
          size: file.size
        });
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  private async processWordFile(file: File): Promise<any> {
    // 处理 Word 文件
    return {
      type: 'document',
      name: file.name,
      path: (file as any).path, // Electron 环境下可用
      size: file.size
    };
  }

  private async processMarkdownFile(file: File): Promise<any> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        resolve({
          type: 'markdown',
          name: file.name,
          content: content,
          size: file.size
        });
      };
      reader.onerror = reject;
      reader.readAsText(file);
    });
  }
}
```

## 8. 部署和运维方案

### 8.1 桌面应用构建和分发

**构建配置 (electron-builder)：**
```json
// package.json
{
  "name": "book-editor",
  "version": "1.0.0",
  "description": "专业的图书编辑器",
  "main": "build/main/main.js",
  "homepage": "./",
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "build-electron": "npm run build && electron-builder",
    "build-electron-all": "npm run build && electron-builder -mwl",
    "dist": "npm run build && electron-builder --publish=never",
    "dist-all": "npm run build && electron-builder -mwl --publish=never",
    "publish": "npm run build && electron-builder --publish=always",
    "electron": "electron .",
    "electron-dev": "ELECTRON_IS_DEV=1 electron .",
    "pack": "electron-builder --dir",
    "prepack": "npm run build"
  },
  "build": {
    "appId": "com.bookeditor.app",
    "productName": "图书编辑器",
    "copyright": "Copyright © 2024 BookEditor Inc.",
    "directories": {
      "output": "dist"
    },
    "files": [
      "build/**/*",
      "node_modules/**/*",
      "src/main/**/*"
    ],
    "extraResources": [
      {
        "from": "assets",
        "to": "assets",
        "filter": ["**/*"]
      }
    ],
    "mac": {
      "category": "public.app-category.productivity",
      "icon": "assets/icon.icns",
      "hardenedRuntime": true,
      "gatekeeperAssess": false,
      "entitlements": "assets/entitlements.mac.plist",
      "entitlementsInherit": "assets/entitlements.mac.plist",
      "target": [
        {
          "target": "dmg",
          "arch": ["x64", "arm64"]
        },
        {
          "target": "zip",
          "arch": ["x64", "arm64"]
        }
      ]
    },
    "win": {
      "icon": "assets/icon.ico",
      "target": [
        {
          "target": "nsis",
          "arch": ["x64", "ia32"]
        },
        {
          "target": "portable",
          "arch": ["x64", "ia32"]
        }
      ]
    },
    "linux": {
      "icon": "assets/icon.png",
      "category": "Office",
      "target": [
        {
          "target": "AppImage",
          "arch": ["x64"]
        },
        {
          "target": "deb",
          "arch": ["x64"]
        },
        {
          "target": "rpm",
          "arch": ["x64"]
        }
      ]
    },
    "nsis": {
      "oneClick": false,
      "allowToChangeInstallationDirectory": true,
      "createDesktopShortcut": true,
      "createStartMenuShortcut": true,
      "shortcutName": "图书编辑器"
    },
    "publish": [
      {
        "provider": "github",
        "owner": "bookeditor",
        "repo": "desktop-app"
      }
    ]
  }
}
```

**自动更新配置：**
```javascript
// src/main/updater.js
const { autoUpdater } = require('electron-updater');
const { dialog } = require('electron');
const log = require('electron-log');

class UpdateManager {
  constructor(mainWindow) {
    this.mainWindow = mainWindow;
    this.setupAutoUpdater();
  }

  setupAutoUpdater() {
    // 配置日志
    autoUpdater.logger = log;
    autoUpdater.logger.transports.file.level = 'info';

    // 配置更新服务器
    if (process.env.NODE_ENV === 'development') {
      autoUpdater.updateConfigPath = path.join(__dirname, 'dev-app-update.yml');
    }

    // 事件监听
    autoUpdater.on('checking-for-update', () => {
      log.info('检查更新中...');
    });

    autoUpdater.on('update-available', (info) => {
      log.info('发现可用更新:', info);
      this.showUpdateAvailableDialog(info);
    });

    autoUpdater.on('update-not-available', (info) => {
      log.info('当前已是最新版本:', info);
    });

    autoUpdater.on('error', (err) => {
      log.error('更新错误:', err);
      this.showUpdateErrorDialog(err);
    });

    autoUpdater.on('download-progress', (progressObj) => {
      let logMessage = `下载速度: ${progressObj.bytesPerSecond}`;
      logMessage += ` - 已下载 ${progressObj.percent}%`;
      logMessage += ` (${progressObj.transferred}/${progressObj.total})`;
      log.info(logMessage);

      this.sendProgressToRenderer(progressObj);
    });

    autoUpdater.on('update-downloaded', (info) => {
      log.info('更新下载完成:', info);
      this.showUpdateDownloadedDialog(info);
    });
  }

  checkForUpdates() {
    autoUpdater.checkForUpdatesAndNotify();
  }

  showUpdateAvailableDialog(info) {
    dialog.showMessageBox(this.mainWindow, {
      type: 'info',
      title: '更新可用',
      message: `发现新版本 ${info.version}`,
      detail: '新版本正在下载中，下载完成后将通知您安装。',
      buttons: ['确定', '查看更新日志'],
      defaultId: 0
    }).then((result) => {
      if (result.response === 1) {
        // 打开更新日志
        require('electron').shell.openExternal(
          `https://github.com/bookeditor/desktop-app/releases/tag/v${info.version}`
        );
      }
    });
  }

  showUpdateDownloadedDialog(info) {
    dialog.showMessageBox(this.mainWindow, {
      type: 'info',
      title: '更新就绪',
      message: `新版本 ${info.version} 已下载完成`,
      detail: '点击"立即重启"安装更新，或稍后手动重启应用。',
      buttons: ['立即重启', '稍后重启'],
      defaultId: 0
    }).then((result) => {
      if (result.response === 0) {
        autoUpdater.quitAndInstall();
      }
    });
  }

  showUpdateErrorDialog(error) {
    dialog.showMessageBox(this.mainWindow, {
      type: 'error',
      title: '更新失败',
      message: '检查更新时发生错误',
      detail: error.message,
      buttons: ['确定']
    });
  }

  sendProgressToRenderer(progress) {
    this.mainWindow.webContents.send('update-progress', progress);
  }
}

module.exports = UpdateManager;
```

**CI/CD 构建流程：**
```yaml
# .github/workflows/build.yml
name: Build and Release

on:
  push:
    tags:
      - 'v*'
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: [macos-latest, windows-latest, ubuntu-latest]

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build React app
      run: npm run build

    - name: Build Electron app (macOS)
      if: matrix.os == 'macos-latest'
      run: npm run build-electron
      env:
        CSC_LINK: ${{ secrets.CSC_LINK }}
        CSC_KEY_PASSWORD: ${{ secrets.CSC_KEY_PASSWORD }}
        APPLE_ID: ${{ secrets.APPLE_ID }}
        APPLE_ID_PASS: ${{ secrets.APPLE_ID_PASS }}

    - name: Build Electron app (Windows)
      if: matrix.os == 'windows-latest'
      run: npm run build-electron
      env:
        CSC_LINK: ${{ secrets.WIN_CSC_LINK }}
        CSC_KEY_PASSWORD: ${{ secrets.WIN_CSC_KEY_PASSWORD }}

    - name: Build Electron app (Linux)
      if: matrix.os == 'ubuntu-latest'
      run: npm run build-electron

    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: ${{ matrix.os }}-build
        path: dist/

  release:
    needs: build
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/')

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Download all artifacts
      uses: actions/download-artifact@v3

    - name: Create Release
      uses: softprops/action-gh-release@v1
      with:
        files: |
          macos-latest-build/*
          windows-latest-build/*
          ubuntu-latest-build/*
        draft: false
        prerelease: false
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
```

### 8.2 后端部署方案

**Docker 容器化：**
```dockerfile
# Dockerfile
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装 Python 依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 收集静态文件
RUN python manage.py collectstatic --noinput

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["gunicorn", "--bind", "0.0.0.0:8000", "--workers", "4", "bookeditor.wsgi:application"]
```

**Docker Compose 配置：**
```yaml
# docker-compose.yml
version: '3.8'

services:
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: bookeditor
      POSTGRES_USER: bookeditor
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  web:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DEBUG=False
      - DATABASE_URL=postgresql://bookeditor:${DB_PASSWORD}@db:5432/bookeditor
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
    depends_on:
      - db
      - redis
    volumes:
      - media_files:/app/media
      - static_files:/app/static

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - static_files:/var/www/static
      - media_files:/var/www/media
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - web

  celery:
    build: .
    command: celery -A bookeditor worker -l info
    environment:
      - DATABASE_URL=postgresql://bookeditor:${DB_PASSWORD}@db:5432/bookeditor
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
    volumes:
      - media_files:/app/media

  celery-beat:
    build: .
    command: celery -A bookeditor beat -l info
    environment:
      - DATABASE_URL=postgresql://bookeditor:${DB_PASSWORD}@db:5432/bookeditor
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis

volumes:
  postgres_data:
  redis_data:
  media_files:
  static_files:
```

**Nginx 配置：**
```nginx
# nginx.conf
events {
    worker_connections 1024;
}

http {
    upstream django {
        server web:8000;
    }

    server {
        listen 80;
        server_name bookeditor.com www.bookeditor.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name bookeditor.com www.bookeditor.com;

        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;

        client_max_body_size 100M;

        location / {
            proxy_pass http://django;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /ws/ {
            proxy_pass http://django;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /static/ {
            alias /var/www/static/;
            expires 30d;
            add_header Cache-Control "public, immutable";
        }

        location /media/ {
            alias /var/www/media/;
            expires 7d;
            add_header Cache-Control "public";
        }
    }
}
```

### 8.3 监控和日志

**应用监控配置：**
```python
# settings/production.py
import sentry_sdk
from sentry_sdk.integrations.django import DjangoIntegration
from sentry_sdk.integrations.celery import CeleryIntegration

# Sentry 错误监控
sentry_sdk.init(
    dsn=os.environ.get('SENTRY_DSN'),
    integrations=[
        DjangoIntegration(),
        CeleryIntegration(),
    ],
    traces_sample_rate=0.1,
    send_default_pii=True
)

# 日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/var/log/bookeditor/django.log',
            'maxBytes': 1024*1024*15,  # 15MB
            'backupCount': 10,
            'formatter': 'verbose',
        },
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'simple'
        },
    },
    'root': {
        'handlers': ['console', 'file'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False,
        },
        'bookeditor': {
            'handlers': ['console', 'file'],
            'level': 'DEBUG',
            'propagate': False,
        },
    },
}
```

**健康检查端点：**
```python
# views/health.py
from django.http import JsonResponse
from django.db import connection
from django.core.cache import cache
import redis

def health_check(request):
    """系统健康检查"""
    status = {
        'status': 'healthy',
        'timestamp': timezone.now().isoformat(),
        'services': {}
    }

    # 检查数据库
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        status['services']['database'] = 'healthy'
    except Exception as e:
        status['services']['database'] = f'unhealthy: {str(e)}'
        status['status'] = 'unhealthy'

    # 检查 Redis
    try:
        cache.set('health_check', 'ok', 10)
        cache.get('health_check')
        status['services']['redis'] = 'healthy'
    except Exception as e:
        status['services']['redis'] = f'unhealthy: {str(e)}'
        status['status'] = 'unhealthy'

    # 检查磁盘空间
    import shutil
    try:
        disk_usage = shutil.disk_usage('/')
        free_space_gb = disk_usage.free / (1024**3)
        if free_space_gb < 1:  # 少于1GB
            status['services']['disk'] = f'warning: {free_space_gb:.2f}GB free'
        else:
            status['services']['disk'] = f'healthy: {free_space_gb:.2f}GB free'
    except Exception as e:
        status['services']['disk'] = f'error: {str(e)}'

    return JsonResponse(status)
```

## 9. 开发计划和里程碑

### 9.1 项目时间规划

**总体时间安排：8-10个月**

### 9.2 详细开发阶段

**第一阶段：基础架构搭建（6-8周）**

*Week 1-2: 项目初始化*
- [ ] 创建项目仓库和基础结构
- [ ] 配置开发环境（Node.js, Python, PostgreSQL）
- [ ] 搭建 Electron + React 开发框架
- [ ] 集成 @grapesjs/react 基础编辑器
- [ ] 设置 Django 后端项目结构

*Week 3-4: 数据库和API基础*
- [ ] 设计并实现数据库模型
- [ ] 创建数据库迁移脚本
- [ ] 实现用户认证系统
- [ ] 开发基础 REST API 端点
- [ ] 配置 Django Channels 用于 WebSocket

*Week 5-6: 前端基础架构*
- [ ] 实现 Electron 主进程和渲染进程通信
- [ ] 创建主要 React 组件结构
- [ ] 集成状态管理（Zustand）
- [ ] 实现基础路由和导航
- [ ] 配置 UI 组件库（Ant Design）

**第二阶段：核心编辑功能（8-10周）**

*Week 7-9: 图书结构管理*
- [ ] 实现图书项目的创建和管理
- [ ] 开发章节树形结构组件
- [ ] 实现页面管理和导航
- [ ] 创建图书设置和配置功能
- [ ] 实现基础的保存和加载功能

*Week 10-12: GrapesJS 编辑器定制*
- [ ] 开发图书专用 GrapesJS 组件
- [ ] 实现自定义块管理器
- [ ] 创建样式管理器定制
- [ ] 实现组件属性面板
- [ ] 集成富文本编辑器

*Week 13-15: 桌面端特有功能*
- [ ] 实现原生菜单栏和快捷键
- [ ] 开发本地文件导入/导出功能
- [ ] 集成系统级拖拽支持
- [ ] 实现原生对话框集成
- [ ] 开发离线模式支持

*Week 16: 第二阶段测试和优化*
- [ ] 功能测试和 Bug 修复
- [ ] 性能优化
- [ ] 用户体验改进
- [ ] 代码重构和文档更新

**第三阶段：协作和高级功能（8-10周）**

*Week 17-19: 实时协作系统*
- [ ] 实现 WebSocket 实时通信
- [ ] 开发操作转换算法
- [ ] 实现多用户编辑冲突处理
- [ ] 创建用户状态和光标显示
- [ ] 实现编辑锁定机制

*Week 20-22: 版本控制系统*
- [ ] 实现自动保存机制
- [ ] 开发版本历史和比较功能
- [ ] 创建版本回滚功能
- [ ] 实现变更追踪和差异显示
- [ ] 开发分支管理（可选）

*Week 23-25: 模板和AI功能*
- [ ] 开发模板系统和模板库
- [ ] 实现模板应用和预览
- [ ] 集成 AI 辅助内容生成
- [ ] 实现智能排版建议
- [ ] 开发语法检查和优化

*Week 26: 第三阶段测试和优化*
- [ ] 协作功能测试
- [ ] 性能压力测试
- [ ] AI 功能调优
- [ ] 安全性测试

**第四阶段：优化和发布（4-6周）**

*Week 27-28: 性能优化*
- [ ] 编辑器性能优化
- [ ] 内存使用优化
- [ ] 网络请求优化
- [ ] 数据库查询优化
- [ ] 应用启动速度优化

*Week 29-30: 发布准备*
- [ ] 实现自动更新机制
- [ ] 配置跨平台打包
- [ ] 设置 CI/CD 流程
- [ ] 完成应用签名和公证
- [ ] 创建安装程序

*Week 31-32: 测试和发布*
- [ ] 全面功能测试
- [ ] 用户验收测试
- [ ] Bug 修复和最终优化
- [ ] 正式版本发布
- [ ] 用户文档和教程

### 9.3 关键里程碑

**里程碑 1：MVP 版本（第8周）**
- 基础的图书创建和编辑功能
- 简单的页面管理
- 本地保存和加载
- 基础的 GrapesJS 编辑器

**里程碑 2：Beta 版本（第16周）**
- 完整的编辑功能
- 桌面端特有功能
- 基础的导入导出
- 用户界面完善

**里程碑 3：协作版本（第26周）**
- 实时协作功能
- 版本控制系统
- 模板系统
- AI 辅助功能

**里程碑 4：正式版本（第32周）**
- 性能优化完成
- 自动更新机制
- 跨平台支持
- 用户文档完善

### 9.4 风险评估和应对策略

**技术风险：**
1. **GrapesJS 定制复杂度**
   - 风险：自定义组件开发可能比预期复杂
   - 应对：提前进行技术验证，准备备选方案

2. **实时协作技术难度**
   - 风险：操作转换算法实现困难
   - 应对：采用成熟的协作库，如 ShareJS 或 Yjs

3. **跨平台兼容性**
   - 风险：不同操作系统的兼容性问题
   - 应对：早期进行跨平台测试，使用标准化的 API

**项目风险：**
1. **开发时间超期**
   - 风险：功能复杂度超出预期
   - 应对：采用敏捷开发，优先实现核心功能

2. **团队协作问题**
   - 风险：前后端协作不顺畅
   - 应对：建立清晰的 API 规范和沟通机制

3. **用户需求变更**
   - 风险：开发过程中需求频繁变更
   - 应对：建立需求变更管理流程

### 9.5 质量保证计划

**代码质量：**
- 使用 ESLint 和 Prettier 进行代码规范
- 实施代码审查制度
- 维持 80% 以上的测试覆盖率
- 使用 TypeScript 提供类型安全

**测试策略：**
- 单元测试：Jest + React Testing Library
- 集成测试：Cypress 端到端测试
- 性能测试：Lighthouse 和自定义性能监控
- 安全测试：OWASP 安全检查

**持续集成：**
- GitHub Actions 自动化构建和测试
- 自动化部署到测试环境
- 代码质量门禁
- 自动化安全扫描

---

## 总结

本方案详细规划了基于 GrapesJS 的桌面端图书编辑器系统的完整技术架构和实施计划。通过 React + GrapesJS + Electron 的前端技术栈和 Django 的后端架构，我们能够构建一个功能完整、性能优秀的专业图书编辑工具。

**核心优势：**
1. **桌面端原生体验**：充分利用 Electron 提供的桌面应用能力
2. **强大的编辑功能**：基于 GrapesJS 的所见即所得编辑器
3. **实时协作能力**：支持多用户在线协作编辑
4. **完整的版本控制**：自动保存和版本管理
5. **AI 智能辅助**：集成 AI 提升编辑效率
6. **跨平台支持**：Windows、macOS、Linux 全平台覆盖

**技术特色：**
- 模块化架构设计，易于扩展和维护
- 现代化技术栈，保证长期技术先进性
- 完善的错误处理和用户体验
- 企业级的安全性和稳定性

该方案为图书编辑器的开发提供了清晰的技术路线图和实施计划，确保项目能够按时交付并满足用户需求。
```
